// API Configuration
export const API_CONFIG = {
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const

// Cache Configuration
export const CACHE_CONFIG = {
  DEFAULT_STALE_TIME: 5 * 60 * 1000, // 5 minutes
  DEFAULT_GC_TIME: 10 * 60 * 1000, // 10 minutes
} as const

// UI Configuration
export const UI_CONFIG = {
  DEBOUNCE_DELAY: 300,
  ANIMATION_DURATION: 200,
  TOAST_DURATION: 5000,
} as const

// Pagination Configuration
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 25, 50, 100],
} as const

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  DISPLAY_WITH_TIME: 'MMM dd, yyyy HH:mm',
  INPUT: 'yyyy-MM-dd',
  API: 'yyyy-MM-dd\'T\'HH:mm:ss.SSSxxx',
} as const

// Query Keys for React Query
export const QUERY_KEYS = {
  AUTH: ['auth'],
  USERS: ['users'],
  OUTLETS: ['outlets'],
  SALES: ['sales'],
  DASHBOARD: ['dashboard'],
  PROFILE: ['profile'],
  SETTINGS: ['settings'],
  AUDIT_LOGS: ['audit-logs'],
} as const

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'broku_auth_token',
  USER_PREFERENCES: 'broku_user_preferences',
  THEME: 'broku_theme',
} as const

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  STAFF: 'staff',
  USER: 'user',
} as const

// Risk Levels
export const RISK_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
} as const

// Trend Directions
export const TREND_DIRECTIONS = {
  UP: 'up',
  DOWN: 'down',
  STABLE: 'stable',
} as const

// Sort Orders
export const SORT_ORDERS = {
  ASC: 'asc',
  DESC: 'desc',
} as const

// Environment Types
export const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test',
} as const
// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/v1/auth/login',
    SIGNUP: '/api/v1/auth/register',
    LOGOUT: '/api/v1/auth/logout',
    REFRESH: '/api/v1/auth/refresh',
    ME: '/api/v1/auth/me',
  },
  USERS: {
    LIST: '/api/v1/users',
    GET: (id: string) => `/api/v1/users/${id}`,
    CREATE: '/api/v1/users',
    UPDATE: (id: string) => `/api/v1/users/${id}`,
    DELETE: (id: string) => `/api/v1/users/${id}`,
  },
  OUTLETS: {
    LIST: '/api/v1/outlets',
    GET: (id: string) => `/api/v1/outlets/${id}`,
    CREATE: '/api/v1/outlets',
    UPDATE: (id: string) => `/api/v1/outlets/${id}`,
    DELETE: (id: string) => `/api/v1/outlets/${id}`,
  },
  SALES: {
    LIST: '/api/v1/sales',
    GET: (id: string) => `/api/v1/sales/${id}`,
    CREATE: '/api/v1/sales',
    UPDATE: (id: string) => `/api/v1/sales/${id}`,
    DELETE: (id: string) => `/api/v1/sales/${id}`,
  },
  DASHBOARD: {
    ADMIN: '/api/v1/dashboard/admin',
    USER: '/api/v1/dashboard/user',
  },
  AUDIT_LOGS: {
    LIST: '/api/v1/audit-logs',
  },
  WHATSAPP: {
    LOGIN: '/api/v1/whatsapp/login',
    LOGOUT: '/api/v1/whatsapp/logout',
    STATUS: '/api/v1/whatsapp/status',
  },
} as const;
