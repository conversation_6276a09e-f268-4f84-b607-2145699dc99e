import { User, UserRole } from './common'

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  fullName: string
  role?: UserRole
  outletId?: number
}

export interface AuthResponse {
  user: User
  token: string
  expiresIn: string
}

export interface JwtPayload {
  userId: number
  username: string
  email: string
  role: UserRole
  outletId?: number
  iat?: number
  exp?: number
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface ResetPasswordRequest {
  email: string
}
