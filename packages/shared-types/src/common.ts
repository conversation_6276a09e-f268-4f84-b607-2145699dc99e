export type UserRole = 'admin' | 'staff' | 'user'

export interface BaseEntity {
  id: string
  createdAt: string
  updatedAt: string
  deletedAt?: string | null
}

export interface User {
  userId: number
  username: string
  email: string
  fullName: string
  role: UserRole
  outletId: number | null
  isActive?: boolean
  lastLoginAt?: string | null
}

export interface outlet extends BaseEntity {
  outletId: number
  name: string
  address: string
  phone: string
  email?: string
  managerId?: string
  isActive: boolean
}

export interface Sale extends BaseEntity {
  salesId: number
  outletId: number
  userId: number
  salesDate: string
  cashAmount: number
  cardAmount: number
  onlinePaymentAmount: number
  totalSales: number
  totalDiscounts: number
  notes?: string
  outlet?: outlet
  user?: User
}

export interface DateRange {
  from: Date
  to: Date
}

export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResult<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}
