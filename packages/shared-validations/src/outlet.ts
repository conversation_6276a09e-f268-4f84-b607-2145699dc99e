import { z } from 'zod'
import { nameSchema, phoneSchema, addressSchema, emailSchema, idSchema, paginationSchema } from './base'

// outlet form validation schema
export const outletFormSchema = z.object({
  name: nameSchema,
  address: addressSchema,
  phone: phoneSchema,
  email: emailSchema.optional().or(z.literal('')),
  managerId: idSchema.optional().or(z.literal('')),
})

// outlet update validation schema
export const outletUpdateSchema = z.object({
  name: nameSchema.optional(),
  address: addressSchema.optional(),
  phone: phoneSchema.optional(),
  email: emailSchema.optional().or(z.literal('')),
  managerId: idSchema.optional().or(z.literal('')),
  isActive: z.boolean().optional(),
})

// outlet creation validation schema
export const createOutletSchema = z.object({
  name: nameSchema,
  address: addressSchema,
  phone: phoneSchema,
  email: emailSchema.optional(),
  managerId: z.number().positive().optional(),
})

// outlet filters validation
export const outletFiltersSchema = z.object({
  isActive: z.boolean().optional(),
  managerId: z.number().positive().optional(),
  search: z.string().optional(),
}).merge(paginationSchema)

// outlet ID parameter validation
export const outletIdParamSchema = z.object({
  id: z.string().transform((val) => parseInt(val, 10)).refine((val) => !isNaN(val) && val > 0, {
    message: 'Outlet ID must be a positive number',
  }),
})
