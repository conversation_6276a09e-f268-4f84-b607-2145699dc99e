import { z } from 'zod'
import { emailSchema, passwordSchema, nameSchema, paginationSchema } from './base'
import { usernameSchema, roleSchema, outletIdSchema } from './auth'

// User form validation schema
export const userFormSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
  confirmPassword: z.string().min(1, 'Please confirm your password'),
  fullName: nameSchema,
  role: roleSchema,
  outletId: outletIdSchema,
  isActive: z.boolean().default(true),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

// User edit form validation schema (password optional)
export const userEditFormSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  fullName: nameSchema,
  role: roleSchema,
  outletId: outletIdSchema,
  isActive: z.boolean().default(true),
})

// Create user request validation
export const createUserRequestSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
  fullName: nameSchema,
  role: roleSchema,
  outletId: outletIdSchema,
})

// Update user request validation
export const updateUserRequestSchema = z.object({
  username: usernameSchema.optional(),
  email: emailSchema.optional(),
  fullName: nameSchema.optional(),
  role: roleSchema.optional(),
  outletId: outletIdSchema,
  isActive: z.boolean().optional(),
})

// User filters validation
export const userFiltersSchema = z.object({
  role: roleSchema.optional(),
  outletId: z.number().positive().optional(),
  isActive: z.boolean().optional(),
  search: z.string().optional(),
}).merge(paginationSchema)

// Bulk user operation validation
export const bulkUserOperationSchema = z.object({
  userIds: z.array(z.number().positive()).min(1, 'At least one user must be selected'),
  action: z.enum(['activate', 'deactivate', 'delete']),
})

// Password reset request validation
export const passwordResetRequestSchema = z.object({
  email: emailSchema,
})

// Email check validation
export const emailCheckSchema = z.object({
  email: emailSchema,
})
