import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import {
  Sale,
  SaleFormData,
  SalesListResponse,
  SalesReport,
  SalesMetrics,
  SalesFiltersData,
  SalesReportFiltersData,
  SalesTarget,
  SalesTargetData,
} from '../types';

export const salesApi = {
  // CRUD operations
  getSales: async (filters?: Partial<SalesFiltersData>): Promise<SalesListResponse> => {
    const response = await apiClient.get(API_ENDPOINTS.SALES.LIST, {
      params: filters,
    });
    return response.data;
  },

  getSale: async (id: string): Promise<Sale> => {
    const response = await apiClient.get(API_ENDPOINTS.SALES.GET(id));
    return response.data;
  },

  createSale: async (data: SaleFormData): Promise<Sale> => {
    const response = await apiClient.post(API_ENDPOINTS.SALES.CREATE, data);
    return response.data;
  },

  updateSale: async (id: string, data: Partial<SaleFormData>): Promise<Sale> => {
    const response = await apiClient.put(API_ENDPOINTS.SALES.UPDATE(id), data);
    return response.data;
  },

  deleteSale: async (id: string): Promise<void> => {
    await apiClient.delete(API_ENDPOINTS.SALES.DELETE(id));
  },

  // Bulk operations
  bulkCreateSales: async (sales: SaleFormData[]): Promise<Sale[]> => {
    const response = await apiClient.post(`${API_ENDPOINTS.SALES.CREATE}/bulk`, { sales });
    return response.data;
  },

  bulkDeleteSales: async (ids: string[]): Promise<void> => {
    await apiClient.delete(`${API_ENDPOINTS.SALES.LIST}/bulk`, { data: { ids } });
  },

  // Reports and analytics
  getSalesReport: async (filters?: SalesReportFiltersData): Promise<SalesReport> => {
    const response = await apiClient.get(API_ENDPOINTS.SALES.REPORTS, {
      params: filters,
    });
    return response.data;
  },

  getSalesMetrics: async (period?: string): Promise<SalesMetrics> => {
    const response = await apiClient.get(`${API_ENDPOINTS.SALES.REPORTS}/metrics`, {
      params: { period },
    });
    return response.data;
  },

  getSalesTrend: async (period: string = '30d'): Promise<any[]> => {
    const response = await apiClient.get(`${API_ENDPOINTS.SALES.REPORTS}/trend`, {
      params: { period },
    });
    return response.data;
  },

  getTopoutlets: async (limit: number = 10): Promise<any[]> => {
    const response = await apiClient.get(`${API_ENDPOINTS.SALES.REPORTS}/top-outlets`, {
      params: { limit },
    });
    return response.data;
  },

  getTopUsers: async (limit: number = 10): Promise<any[]> => {
    const response = await apiClient.get(`${API_ENDPOINTS.SALES.REPORTS}/top-users`, {
      params: { limit },
    });
    return response.data;
  },

  // Targets
  getSalesTargets: async (userId?: string): Promise<SalesTarget[]> => {
    const response = await apiClient.get(`${API_ENDPOINTS.SALES.LIST}/targets`, {
      params: { userId },
    });
    return response.data;
  },

  createSalesTarget: async (data: SalesTargetData): Promise<SalesTarget> => {
    const response = await apiClient.post(`${API_ENDPOINTS.SALES.LIST}/targets`, data);
    return response.data;
  },

  updateSalesTarget: async (id: string, data: Partial<SalesTargetData>): Promise<SalesTarget> => {
    const response = await apiClient.put(`${API_ENDPOINTS.SALES.LIST}/targets/${id}`, data);
    return response.data;
  },

  deleteSalesTarget: async (id: string): Promise<void> => {
    await apiClient.delete(`${API_ENDPOINTS.SALES.LIST}/targets/${id}`);
  },

  // Export
  exportSales: async (filters?: any): Promise<Blob> => {
    const response = await apiClient.get(`${API_ENDPOINTS.SALES.REPORTS}/export`, {
      params: filters,
      responseType: 'blob',
    });
    return response.data;
  },

  // Import
  importSales: async (file: File): Promise<{ success: number; errors: any[] }> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post(`${API_ENDPOINTS.SALES.CREATE}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};
