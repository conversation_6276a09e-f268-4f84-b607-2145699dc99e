import { z } from 'zod';
import {
  currencySchema,
  dateSchema,
  idSchema,
  paginationSchema,
  searchSchema,
} from '@/shared/validations/baseSchemas';

export const saleFormSchema = z.object({
  outletId: idSchema,
  amount: currencySchema,
  date: dateSchema,
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
});

export const saleUpdateSchema = z.object({
  outletId: idSchema.optional(),
  amount: currencySchema.optional(),
  date: dateSchema.optional(),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
});

export const salesFiltersSchema = z
  .object({
    outletId: idSchema.optional(),
    userId: idSchema.optional(),
    dateFrom: dateSchema.optional(),
    dateTo: dateSchema.optional(),
    minAmount: z.number().min(0).optional(),
    maxAmount: z.number().min(0).optional(),
    search: z.string().optional(),
  })
  .merge(paginationSchema);

export const salesReportFiltersSchema = z.object({
  period: z.enum(['day', 'week', 'month', 'year']).default('month'),
  dateFrom: dateSchema.optional(),
  dateTo: dateSchema.optional(),
  outletId: idSchema.optional(),
  userId: idSchema.optional(),
});

export const salesTargetSchema = z
  .object({
    userId: idSchema,
    outletId: idSchema.optional(),
    targetAmount: currencySchema,
    targetSales: z.number().min(1, 'Target sales must be at least 1'),
    period: z.enum(['daily', 'weekly', 'monthly', 'yearly']),
    startDate: dateSchema,
    endDate: dateSchema,
  })
  .refine(
    data => {
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);
      return end > start;
    },
    {
      message: 'End date must be after start date',
      path: ['endDate'],
    }
  );

export const bulkSalesImportSchema = z.object({
  sales: z.array(saleFormSchema).min(1, 'At least one sale is required'),
});

export const salesExportSchema = z.object({
  format: z.enum(['csv', 'xlsx', 'pdf']).default('csv'),
  dateFrom: dateSchema.optional(),
  dateTo: dateSchema.optional(),
  outletId: idSchema.optional(),
  userId: idSchema.optional(),
  includeDetails: z.boolean().default(true),
});

// Type exports
export type SaleFormData = z.infer<typeof saleFormSchema>;
export type SaleUpdateData = z.infer<typeof saleUpdateSchema>;
export type SalesFiltersData = z.infer<typeof salesFiltersSchema>;
export type SalesReportFiltersData = z.infer<typeof salesReportFiltersSchema>;
export type SalesTargetData = z.infer<typeof salesTargetSchema>;
export type BulkSalesImportData = z.infer<typeof bulkSalesImportSchema>;
export type SalesExportData = z.infer<typeof salesExportSchema>;
