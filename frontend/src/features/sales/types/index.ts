import { Sale as BaseSale, outlet, User } from '@/shared/types';

export interface Sale extends BaseSale {}

export interface SaleFormData {
  outletId: string;
  amount: number;
  date: string;
  description?: string;
}

export interface SaleFilters {
  outletId?: string;
  userId?: string;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
}

export interface SalesReport {
  period: 'day' | 'week' | 'month' | 'year';
  totalSales: number;
  totalRevenue: number;
  averageOrderValue: number;
  salesByoutlet: Array<{
    outlet: outlet;
    totalSales: number;
    totalRevenue: number;
  }>;
  salesByUser: Array<{
    user: User;
    totalSales: number;
    totalRevenue: number;
  }>;
  salesTrend: Array<{
    date: string;
    sales: number;
    revenue: number;
  }>;
}

export interface SalesMetrics {
  todaySales: number;
  todayRevenue: number;
  monthSales: number;
  monthRevenue: number;
  yearSales: number;
  yearRevenue: number;
  growth: {
    salesGrowth: number;
    revenueGrowth: number;
  };
}

export interface SaleWithDetails extends Sale {
  outlet: outlet;
  user: User;
}

export interface SalesListResponse {
  sales: SaleWithDetails[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SalesChartData {
  date: string;
  sales: number;
  revenue: number;
  [key: string]: any;
}

export interface outletSalesData {
  outletId: string;
  outletName: string;
  totalSales: number;
  totalRevenue: number;
  averageOrderValue: number;
  salesCount: number;
}

export interface UserSalesData {
  userId: string;
  userName: string;
  totalSales: number;
  totalRevenue: number;
  salesCount: number;
  averageOrderValue: number;
}

export interface SalesTarget {
  id: string;
  userId: string;
  outletId?: string;
  targetAmount: number;
  targetSales: number;
  period: 'daily' | 'weekly' | 'monthly' | 'yearly';
  startDate: string;
  endDate: string;
  achieved: {
    amount: number;
    sales: number;
  };
  percentage: {
    amount: number;
    sales: number;
  };
}

// Alias types for compatibility with existing imports
export type SalesFiltersData = SaleFilters;
export type SalesReportFiltersData = SaleFilters & {
  reportType?: 'summary' | 'detailed';
  groupBy?: 'outlet' | 'user' | 'date';
};
export type SalesTargetData = SalesTarget;
