import React from 'react';
import { cn } from '@/lib/utils';
import { SortableColumnHeader } from './SortIcon';
import { RiskBadge } from './RiskBadge';
import type { outletDataItem, outletSortKeys, outletSortConfig } from '../types';
import { formatCurrency, getAdjustmentColor } from '../utils/formatters';
import { outlet_TABLE_COLUMNS } from '../utils/constants';

interface outletTableProps {
  data: outletDataItem[];
  sortConfig: outletSortConfig;
  onSort: (key: outletSortKeys) => void;
  className?: string;
  maxHeight?: string;
}

/**
 * outletTable component for displaying outlet revenue data
 */
export const outletTable = React.memo<outletTableProps>(
  ({ data, sortConfig, onSort, className, maxHeight = 'max-h-96' }) => {
    if (!data || data.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <p className="font-semibold text-slate-700">No outlet Data</p>
          <p className="text-sm text-slate-500">
            outlet revenue data is not available at this time.
          </p>
        </div>
      );
    }

    return (
      <div className={cn('overflow-hidden', className)}>
        {/* Table Header */}
        <div className="grid grid-cols-5 gap-4 border-b pb-2 text-sm font-medium text-slate-500">
          {outlet_TABLE_COLUMNS.map(column => (
            <div key={column.key} className={column.className}>
              {column.sortable ? (
                <SortableColumnHeader
                  sortKey={column.key}
                  currentSortKey={sortConfig.key}
                  sortDirection={sortConfig.direction}
                  onSort={onSort}
                  align={column.align}
                >
                  {column.label}
                </SortableColumnHeader>
              ) : (
                <span
                  className={cn(
                    'text-sm font-medium text-slate-500',
                    column.align === 'center' && 'text-center',
                    column.align === 'right' && 'text-right'
                  )}
                >
                  {column.label}
                </span>
              )}
            </div>
          ))}
        </div>

        {/* Table Body */}
        <div className={cn('mt-2 space-y-1 overflow-y-auto', maxHeight)}>
          {data.map((item, index) => (
            <outletTableRow key={`${item.outlet}-${index}`} item={item} />
          ))}
        </div>
      </div>
    );
  }
);

outletTable.displayName = 'outletTable';

/**
 * Individual table row component
 */
const outletTableRow = React.memo<{ item: outletDataItem }>(({ item }) => {
  return (
    <div
      className="grid grid-cols-5 items-center gap-4 rounded-md py-2 text-sm transition-colors focus-within:bg-gray-50 hover:bg-gray-50"
      tabIndex={0}
      aria-label={`outlet: ${item.outlet}, Actual: ${formatCurrency(item.actual)}, Risk: ${item.risk}`}
    >
      {/* outlet Name and Forecast */}
      <div className="col-span-2">
        <p className="truncate font-semibold text-slate-800" title={item.outlet}>
          {item.outlet}
        </p>
        <p className="text-xs text-slate-500">FC: {formatCurrency(item.forecast)}</p>
      </div>

      {/* Q3 Adjusted */}
      <div
        className={cn(
          'text-right font-medium',
          item.q3Adjusted != null ? getAdjustmentColor(item.q3Adjusted) : ''
        )}
        title={`Q3 Adjusted: ${item.q3Adjusted != null ? formatCurrency(item.q3Adjusted) : 'N/A'}`}
      >
        {item.q3Adjusted != null ? formatCurrency(item.q3Adjusted) : 'N/A'}
      </div>

      {/* Actual Revenue */}
      <div
        className="text-right font-medium text-slate-800"
        title={`Actual Revenue: ${formatCurrency(item.actual)}`}
      >
        {formatCurrency(item.actual)}
      </div>

      {/* Risk Badge */}
      <div className="text-center">
        <RiskBadge risk={item.risk} />
      </div>
    </div>
  );
});

outletTableRow.displayName = 'outletTableRow';

/**
 * Compact version of the outlet table for smaller screens
 */
export const CompactoutletTable = React.memo<outletTableProps>(({ data, className }) => {
  if (!data || data.length === 0) {
    return (
      <div className="py-4 text-center">
        <p className="text-slate-500">No data available</p>
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      {data.map((item, index) => (
        <div key={`${item.outlet}-${index}`} className="rounded-lg bg-gray-50 p-3">
          <div className="mb-2 flex items-start justify-between">
            <h4 className="truncate font-semibold text-slate-800">{item.outlet}</h4>
            <RiskBadge risk={item.risk} />
          </div>
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span className="text-slate-500">Forecast:</span>
              <span className="ml-1 font-medium">{formatCurrency(item.forecast)}</span>
            </div>
            <div>
              <span className="text-slate-500">Actual:</span>
              <span className="ml-1 font-medium">{formatCurrency(item.actual)}</span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
});

CompactoutletTable.displayName = 'CompactoutletTable';
