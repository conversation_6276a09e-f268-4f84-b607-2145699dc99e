import type {
  outletDataItem,
  TargetDataItem,
  outletSortKeys,
  SortDirection,
  TargetSortOrder,
  RiskLevel,
} from '../types';

/**
 * Risk level ordering for sorting
 */
const RISK_ORDER: Record<RiskLevel, number> = {
  low: 1,
  medium: 2,
  high: 3,
};

/**
 * Sorts outlet data based on the provided configuration
 */
export const sortoutletData = (
  data: outletDataItem[],
  key: outletSortKeys,
  direction: SortDirection
): outletDataItem[] => {
  if (!data || data.length === 0) return [];

  const sortableItems = [...data];

  sortableItems.sort((a, b) => {
    const aValue = a[key];
    const bValue = b[key];
    let comparison = 0;

    if (key === 'risk') {
      comparison = RISK_ORDER[aValue as RiskLevel] - RISK_ORDER[bValue as RiskLevel];
    } else if (typeof aValue === 'string' && typeof bValue === 'string') {
      comparison = aValue.localeCompare(bValue, 'en', {
        numeric: true,
        sensitivity: 'base',
      });
    } else if (typeof aValue === 'number' && typeof bValue === 'number') {
      comparison = aValue - bValue;
    }

    return direction === 'descending' ? comparison * -1 : comparison;
  });

  return sortableItems;
};

/**
 * Sorts target data based on percentage
 */
export const sortTargetData = (
  data: TargetDataItem[],
  sortOrder: TargetSortOrder
): TargetDataItem[] => {
  if (!data || data.length === 0) return [];

  const sortableItems = [...data];

  sortableItems.sort((a, b) => {
    const comparison = a.percentage - b.percentage;
    return sortOrder === 'asc' ? comparison : comparison * -1;
  });

  return sortableItems;
};

/**
 * Filters target data based on search query
 */
export const filterTargetData = (data: TargetDataItem[], searchQuery: string): TargetDataItem[] => {
  if (!data || data.length === 0) return [];
  if (!searchQuery.trim()) return data;

  const query = searchQuery.toLowerCase().trim();

  return data.filter(item => item.outlet.toLowerCase().includes(query));
};

/**
 * Combines filtering and sorting for target data
 */
export const filterAndSortTargetData = (
  data: TargetDataItem[],
  searchQuery: string,
  sortOrder: TargetSortOrder
): TargetDataItem[] => {
  const filtered = filterTargetData(data, searchQuery);
  return sortTargetData(filtered, sortOrder);
};

/**
 * Gets the next sort direction for a column
 */
export const getNextSortDirection = (
  currentKey: outletSortKeys,
  newKey: outletSortKeys,
  currentDirection: SortDirection
): SortDirection => {
  if (currentKey !== newKey) {
    return 'ascending';
  }
  return currentDirection === 'ascending' ? 'descending' : 'ascending';
};

/**
 * Toggles target sort order
 */
export const toggleTargetSortOrder = (currentOrder: TargetSortOrder): TargetSortOrder => {
  return currentOrder === 'desc' ? 'asc' : 'desc';
};

/**
 * Validates sort key
 */
export const isValidSortKey = (key: string): key is outletSortKeys => {
  return ['outlet', 'q3Adjusted', 'actual', 'risk'].includes(key);
};

/**
 * Validates sort direction
 */
export const isValidSortDirection = (direction: string): direction is SortDirection => {
  return ['ascending', 'descending'].includes(direction);
};

/**
 * Creates a stable sort key for React keys
 */
export const createSortKey = (item: outletDataItem | TargetDataItem): string => {
  return `${item.outlet}-${Date.now()}`;
};

/**
 * Debounced search function
 */
export const createDebouncedSearch = (callback: (query: string) => void, delay: number = 300) => {
  let timeoutId: NodeJS.Timeout;

  return (query: string) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => callback(query), delay);
  };
};

/**
 * Gets sort indicator text for accessibility
 */
export const getSortIndicatorText = (
  key: outletSortKeys,
  currentKey: outletSortKeys,
  direction: SortDirection
): string => {
  if (key !== currentKey) {
    return 'Not sorted';
  }
  return direction === 'ascending' ? 'Sorted ascending' : 'Sorted descending';
};

/**
 * Gets column header accessible label
 */
export const getColumnHeaderLabel = (
  key: outletSortKeys,
  currentKey: outletSortKeys,
  direction: SortDirection
): string => {
  const columnNames: Record<outletSortKeys, string> = {
    id: 'ID',
    outlet: 'outlet',
    actual: 'Actual Revenue',
    target: 'Target',
    forecast: 'Forecast',
    risk: 'Risk Level',
    achievement: 'Achievement',
    trend: 'Trend',
    q3Adjusted: 'Q3 Adjusted',
  };

  const sortStatus = getSortIndicatorText(key, currentKey, direction);
  return `${columnNames[key]}, ${sortStatus}. Click to sort.`;
};
