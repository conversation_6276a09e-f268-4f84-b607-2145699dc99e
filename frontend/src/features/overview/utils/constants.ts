import type { ChartConfig, outletTableColumn } from '../types';

/**
 * Default chart configuration
 */
export const DEFAULT_CHART_CONFIG: ChartConfig = {
  margin: {
    top: 5,
    right: 5,
    left: 5,
    bottom: 5,
  },
  colors: {
    forecast: '#06b6d4', // cyan-500
    actual: '#f59e0b', // amber-500
  },
};

/**
 * Chart legend configuration
 */
export const CHART_LEGEND_ITEMS = [
  {
    color: DEFAULT_CHART_CONFIG.colors.forecast,
    label: 'Forecast',
    dataKey: 'forecast',
  },
  {
    color: DEFAULT_CHART_CONFIG.colors.actual,
    label: 'Actual',
    dataKey: 'actual',
  },
] as const;

/**
 * outlet table column configuration
 */
export const outlet_TABLE_COLUMNS: outletTableColumn[] = [
  {
    key: 'outlet',
    label: 'outlet',
    sortable: true,
    align: 'left',
    className: 'col-span-2',
  },
  {
    key: 'q3Adjusted',
    label: 'Q3 Adj.',
    sortable: true,
    align: 'right',
  },
  {
    key: 'actual',
    label: 'Actual',
    sortable: true,
    align: 'right',
  },
  {
    key: 'risk',
    label: 'Risk',
    sortable: true,
    align: 'center',
  },
];

/**
 * Default refresh intervals (in milliseconds)
 */
export const REFRESH_INTERVALS = {
  DISABLED: 0, // No auto-refresh
  FAST: 10000, // 10 seconds
  DEFAULT: 30000, // 30 seconds
  SLOW: 60000, // 1 minute
  VERY_SLOW: 300000, // 5 minutes
} as const;

/**
 * Refresh interval options for UI display
 */
export const REFRESH_INTERVAL_OPTIONS = [
  { value: REFRESH_INTERVALS.DISABLED, label: 'Off', description: 'Manual refresh only' },
  { value: REFRESH_INTERVALS.FAST, label: '10 seconds', description: 'Fast refresh' },
  { value: REFRESH_INTERVALS.DEFAULT, label: '30 seconds', description: 'Default refresh' },
  { value: REFRESH_INTERVALS.SLOW, label: '1 minute', description: 'Slow refresh' },
  { value: REFRESH_INTERVALS.VERY_SLOW, label: '5 minutes', description: 'Very slow refresh' },
] as const;

/**
 * Keyboard shortcuts
 */
export const KEYBOARD_SHORTCUTS = {
  REFRESH: 'r',
  REFRESH_ALT: 'F5',
} as const;

/**
 * Progress bar thresholds
 */
export const PROGRESS_THRESHOLDS = {
  LOW: 75, // Below this is red
  MEDIUM: 90, // Between low and medium is amber
  HIGH: 100, // Above medium is green
} as const;

/**
 * Risk level display configuration
 */
export const RISK_CONFIG = {
  low: {
    color: 'bg-green-500',
    label: 'Low Risk',
    showIndicator: false,
  },
  medium: {
    color: 'bg-amber-500',
    label: 'Medium Risk',
    showIndicator: true,
  },
  high: {
    color: 'bg-red-500',
    label: 'High Risk',
    showIndicator: true,
  },
} as const;

/**
 * Trend direction display configuration
 */
export const TREND_CONFIG = {
  up: {
    color: 'text-green-500',
    label: 'Trending Up',
    icon: 'arrow-up',
  },
  down: {
    color: 'text-red-500',
    label: 'Trending Down',
    icon: 'arrow-down',
  },
  neutral: {
    color: 'text-slate-400',
    label: 'Neutral Trend',
    icon: 'minus',
  },
} as const;

/**
 * Storage keys for user preferences
 */
export const STORAGE_KEYS = {
  REFRESH_INTERVAL: 'broku_overview_refresh_interval',
  LAST_UPDATED: 'broku_overview_last_updated',
} as const;

/**
 * Default query parameters
 */
export const DEFAULT_QUERY_PARAMS = {
  period: '12m',
  includeForecasts: true,
} as const;

/**
 * Query key constants for React Query (using shared QUERY_KEYS from constants)
 * @deprecated Use QUERY_KEYS from @/shared/utils/constants instead
 */
export const QUERY_KEYS = {
  OVERALL_REVENUE: 'overallRevenue',
  outlet_DATA: 'outletData',
  TARGET_DATA: 'targetData',
  CHART_DATA: 'chartData',
} as const;

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  FETCH_FAILED: 'Failed to fetch revenue data',
  NETWORK_ERROR: 'Network error occurred',
  INVALID_DATA: 'Invalid data received from server',
  UNKNOWN_ERROR: 'An unknown error occurred',
} as const;

/**
 * Loading messages
 */
export const LOADING_MESSAGES = {
  INITIAL: 'Loading revenue data...',
  REFRESHING: 'Refreshing...',
  UPDATING: 'Updating data...',
} as const;

/**
 * Accessibility constants
 */
export const ARIA_LABELS = {
  REFRESH_BUTTON: 'Refresh revenue data',
  SEARCH_INPUT: 'Search outlets',
  SORT_BUTTON: 'Sort table column',
  PROGRESS_BAR: 'Achievement progress',
  RISK_INDICATOR: 'Risk level indicator',
  TREND_INDICATOR: 'Trend direction indicator',
  CHART: 'Revenue forecast vs actual chart',
} as const;

/**
 * CSS class constants
 */
export const CSS_CLASSES = {
  CARD: 'bg-white border-gray-200 shadow-sm',
  BUTTON_PRIMARY: 'bg-blue-600 hover:bg-blue-700 text-white',
  BUTTON_SECONDARY: 'bg-gray-100 hover:bg-gray-200 text-gray-700',
  TEXT_PRIMARY: 'text-slate-800',
  TEXT_SECONDARY: 'text-slate-500',
  TEXT_MUTED: 'text-slate-400',
} as const;

/**
 * Animation durations (in milliseconds)
 */
export const ANIMATION_DURATIONS = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

/**
 * Breakpoints for responsive design
 */
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
} as const;
