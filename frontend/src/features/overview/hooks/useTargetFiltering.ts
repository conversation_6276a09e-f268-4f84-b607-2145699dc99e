import { useState, useMemo, useCallback } from 'react';
import { useDebounce } from '@/shared/hooks/useDebounce';
import { filterAndSortTargetData, toggleTargetSortOrder } from '../utils/sorting';
import type { TargetDataItem, TargetSortOrder, UseTargetFilteringReturn } from '../types';

/**
 * Custom hook for managing target data filtering and sorting
 */
export const useTargetFiltering = (
  data: TargetDataItem[] = [],
  initialSearchQuery: string = '',
  initialSortOrder: TargetSortOrder = 'desc',
  debounceDelay: number = 300
): UseTargetFilteringReturn => {
  const [searchQuery, setSearchQuery] = useState<string>(initialSearchQuery);
  const [sortOrder, setSortOrder] = useState<TargetSortOrder>(initialSortOrder);

  // Debounce search query to avoid excessive filtering
  const debouncedSearchQuery = useDebounce(searchQuery, debounceDelay);

  // Memoized filtered and sorted data
  const filteredData = useMemo(() => {
    return filterAndSortTargetData(data, debouncedSearchQuery, sortOrder);
  }, [data, debouncedSearchQuery, sortOrder]);

  // Handle search query change
  const handleSearchQueryChange = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  // Handle sort order toggle
  const handleToggleSortOrder = useCallback(() => {
    setSortOrder(prevOrder => toggleTargetSortOrder(prevOrder));
  }, []);

  return {
    filteredData,
    searchQuery,
    sortOrder,
    setSortOrder,
    setSearchQuery: handleSearchQueryChange,
    toggleSortOrder: handleToggleSortOrder,
  };
};

/**
 * Hook for target filtering with persistence
 */
export const useTargetFilteringWithPersistence = (
  data: TargetDataItem[] = [],
  storageKey: string = 'target-filters',
  debounceDelay: number = 300
): UseTargetFilteringReturn => {
  // Load initial state from localStorage
  const getInitialState = () => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        return {
          searchQuery: parsed.searchQuery || '',
          sortOrder: ['asc', 'desc'].includes(parsed.sortOrder) ? parsed.sortOrder : 'desc',
        };
      }
    } catch (error) {
      console.warn('Failed to load target filters from localStorage:', error);
    }

    return {
      searchQuery: '',
      sortOrder: 'desc' as TargetSortOrder,
    };
  };

  const initialState = getInitialState();
  const [searchQuery, setSearchQuery] = useState<string>(initialState.searchQuery);
  const [sortOrder, setSortOrder] = useState<TargetSortOrder>(initialState.sortOrder);

  // Debounce search query
  const debouncedSearchQuery = useDebounce(searchQuery, debounceDelay);

  // Memoized filtered and sorted data
  const filteredData = useMemo(() => {
    return filterAndSortTargetData(data, debouncedSearchQuery, sortOrder);
  }, [data, debouncedSearchQuery, sortOrder]);

  // Handle search query change with persistence
  const handleSearchQueryChange = useCallback(
    (query: string) => {
      setSearchQuery(query);

      // Save to localStorage (debounced)
      const timeoutId = setTimeout(() => {
        try {
          const currentState = JSON.parse(localStorage.getItem(storageKey) || '{}');
          localStorage.setItem(
            storageKey,
            JSON.stringify({
              ...currentState,
              searchQuery: query,
            })
          );
        } catch (error) {
          console.warn('Failed to save search query to localStorage:', error);
        }
      }, debounceDelay);

      return () => clearTimeout(timeoutId);
    },
    [storageKey, debounceDelay]
  );

  // Handle sort order toggle with persistence
  const handleToggleSortOrder = useCallback(() => {
    setSortOrder(prevOrder => {
      const newOrder = toggleTargetSortOrder(prevOrder);

      // Save to localStorage
      try {
        const currentState = JSON.parse(localStorage.getItem(storageKey) || '{}');
        localStorage.setItem(
          storageKey,
          JSON.stringify({
            ...currentState,
            sortOrder: newOrder,
          })
        );
      } catch (error) {
        console.warn('Failed to save sort order to localStorage:', error);
      }

      return newOrder;
    });
  }, [storageKey]);

  return {
    filteredData,
    searchQuery,
    sortOrder,
    setSortOrder,
    setSearchQuery: handleSearchQueryChange,
    toggleSortOrder: handleToggleSortOrder,
  };
};

/**
 * Hook for target filtering with advanced search capabilities
 */
export const useAdvancedTargetFiltering = (
  data: TargetDataItem[] = [],
  debounceDelay: number = 300
) => {
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [sortOrder, setSortOrder] = useState<TargetSortOrder>('desc');
  const [minPercentage, setMinPercentage] = useState<number>(0);
  const [maxPercentage, setMaxPercentage] = useState<number>(100);
  const [selectedTrends, setSelectedTrends] = useState<Set<'up' | 'down' | 'neutral'>>(new Set());

  const debouncedSearchQuery = useDebounce(searchQuery, debounceDelay);

  // Advanced filtering logic
  const filteredData = useMemo(() => {
    let filtered = data;

    // Text search
    if (debouncedSearchQuery.trim()) {
      const query = debouncedSearchQuery.toLowerCase().trim();
      filtered = filtered.filter(item => item.outlet.toLowerCase().includes(query));
    }

    // Percentage range filter
    filtered = filtered.filter(
      item => item.percentage >= minPercentage && item.percentage <= maxPercentage
    );

    // Trend filter
    if (selectedTrends.size > 0) {
      filtered = filtered.filter(item => selectedTrends.has(item.trend));
    }

    // Sort
    filtered.sort((a, b) => {
      const comparison = a.percentage - b.percentage;
      return sortOrder === 'asc' ? comparison : comparison * -1;
    });

    return filtered;
  }, [data, debouncedSearchQuery, sortOrder, minPercentage, maxPercentage, selectedTrends]);

  return {
    filteredData,
    searchQuery,
    sortOrder,
    minPercentage,
    maxPercentage,
    selectedTrends,
    setSearchQuery,
    setSortOrder,
    setMinPercentage,
    setMaxPercentage,
    setSelectedTrends,
    toggleSortOrder: () => setSortOrder(prev => toggleTargetSortOrder(prev)),
  };
};
