import { useState, useMemo, useCallback } from 'react';
import { sortoutletData, getNextSortDirection } from '../utils/sorting';
import type {
  outletDataItem,
  outletSortKeys,
  outletSortConfig,
  UseoutletSortingReturn,
} from '../types';

/**
 * Custom hook for managing outlet data sorting
 */
export const useoutletSorting = (
  data: outletDataItem[] = [],
  initialSortConfig: outletSortConfig = {
    key: 'outlet',
    direction: 'ascending',
  }
): UseoutletSortingReturn => {
  const [sortConfig, setSortConfig] = useState<outletSortConfig>(initialSortConfig);

  // Memoized sorted data
  const sortedData = useMemo(() => {
    return sortoutletData(data, sortConfig.key, sortConfig.direction);
  }, [data, sortConfig.key, sortConfig.direction]);

  // Handle sort column click
  const handleSort = useCallback((key: outletSortKeys) => {
    setSortConfig(prevConfig => ({
      key,
      direction: getNextSortDirection(prevConfig.key, key, prevConfig.direction),
    }));
  }, []);

  return {
    sortedData,
    sortConfig,
    setSortConfig,
    handleSort,
  };
};

/**
 * Hook for outlet sorting with persistence
 */
export const useoutletSortingWithPersistence = (
  data: outletDataItem[] = [],
  storageKey: string = 'outlet-sort-config'
): UseoutletSortingReturn => {
  // Load initial sort config from localStorage
  const getInitialSortConfig = (): outletSortConfig => {
    try {
      const stored = localStorage.getItem(storageKey);
      if (stored) {
        const parsed = JSON.parse(stored) as outletSortConfig;
        // Validate the stored config
        if (
          parsed.key &&
          ['outlet', 'q3Adjusted', 'actual', 'risk'].includes(parsed.key) &&
          ['ascending', 'descending'].includes(parsed.direction)
        ) {
          return parsed;
        }
      }
    } catch (error) {
      console.warn('Failed to load sort config from localStorage:', error);
    }

    return {
      key: 'outlet',
      direction: 'ascending',
    };
  };

  const [sortConfig, setSortConfig] = useState<outletSortConfig>(getInitialSortConfig);

  // Memoized sorted data
  const sortedData = useMemo(() => {
    return sortoutletData(data, sortConfig.key, sortConfig.direction);
  }, [data, sortConfig.key, sortConfig.direction]);

  // Handle sort column click with persistence
  const handleSort = useCallback(
    (key: outletSortKeys) => {
      setSortConfig(prevConfig => {
        const newConfig = {
          key,
          direction: getNextSortDirection(prevConfig.key, key, prevConfig.direction),
        };

        // Save to localStorage
        try {
          localStorage.setItem(storageKey, JSON.stringify(newConfig));
        } catch (error) {
          console.warn('Failed to save sort config to localStorage:', error);
        }

        return newConfig;
      });
    },
    [storageKey]
  );

  return {
    sortedData,
    sortConfig,
    setSortConfig,
    handleSort,
  };
};
