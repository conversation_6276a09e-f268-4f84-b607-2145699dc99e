export type RiskLevel = 'low' | 'medium' | 'high';
export type TrendDirection = 'up' | 'down' | 'neutral';
export type SortDirection = 'ascending' | 'descending';
export type TargetSortOrder = 'asc' | 'desc';

export type CurrencyFormatter = (value: number) => string;
export type ColorClassGetter = (value: number) => string;
export type ProgressColorGetter = (percentage: number) => string;
export interface outletPerformanceData {
  id: string;
  outlet: string;
  actual: number;
  target: number;
  forecast: number;
  risk: 'low' | 'medium' | 'high';
  achievement: number;
  trend: 'up' | 'down' | 'neutral';
  q3Adjusted?: number; // Optional Q3 adjustment field
}

// Alias for compatibility with existing code
export type outletDataItem = outletPerformanceData;

export type outletSortKeys = keyof outletPerformanceData;

export interface outletSortConfig {
  key: outletSortKeys;
  direction: 'ascending' | 'descending';
}

export interface ChartDataItem {
  month: string;
  forecast: number;
  actual: number;
}

export interface RevenueOverview {
  actualRevenue: number;
  forecastRevenue: number;
  achievementPercentage: number;
}

export interface OverallRevenueData {
  overview: RevenueOverview;
  chartData: ChartDataItem[];
  performanceData: outletPerformanceData[];
}

export interface OverallRevenueProps {
  className?: string;
  refreshInterval?: number;
  enablePolling?: boolean;
}

export type outletViewMode = 'table' | 'targets' | 'combined';

export interface TargetDataItem {
  outlet: string;
  percentage: number;
  trend: 'up' | 'down' | 'neutral';
}

export interface outletPerformanceCardProps {
  outletData?: outletPerformanceData[];
  targetData?: TargetDataItem[];
  onRefresh?: () => void;
  isRefreshing?: boolean;
  className?: string;
  enableSearch?: boolean;
  enableSorting?: boolean;
  maxHeight?: string;
  defaultView?: outletViewMode;
  allowViewToggle?: boolean;
}

export interface outletPerformanceState {
  sortConfig: outletSortConfig;
  searchQuery: string;
  viewMode: outletViewMode;
}

// Additional missing types
export interface ProgressBarProps {
  value: number;
  className?: string;
  'aria-label'?: string;
}

export interface TrendIconProps {
  trend: TrendDirection;
  className?: string;
  'aria-label'?: string;
}

export interface SortIconProps {
  direction: SortDirection;
  className?: string;
}

export interface RiskBadgeProps {
  risk: RiskLevel;
  className?: string;
}

export interface outletTableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  className?: string;
  align?: 'left' | 'center' | 'right';
}

export interface TargetFilters {
  searchQuery: string;
  sortOrder: TargetSortOrder;
}

export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, unknown>;
}

export interface LoadingStates {
  isLoading: boolean;
  isRefreshing: boolean;
  isError: boolean;
}

export interface OverallRevenueState {
  data: OverallRevenueData | null;
  isLoading: boolean;
  error: ApiError | null;
  lastUpdated: Date | null;
}

export interface UseOverallRevenueReturn {
  data: OverallRevenueData | undefined;
  isLoading: boolean;
  error: ApiError | null;
  refetch: () => void;
}

export interface UseoutletSortingReturn {
  sortConfig: outletSortConfig;
  setSortConfig: (config: outletSortConfig) => void;
  sortedData: outletDataItem[];
  handleSort: (key: keyof outletPerformanceData) => void;
}

export interface UseTargetFilteringReturn {
  filteredData: TargetDataItem[];
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  sortOrder: TargetSortOrder;
  setSortOrder: (order: TargetSortOrder) => void;
  toggleSortOrder: () => void;
}

export interface ChartConfig {
  margin: {
    top: number;
    right: number;
    left: number;
    bottom: number;
  };
  colors: {
    forecast: string;
    actual: string;
  };
}

export interface AccessibilityProps {
  'aria-label'?: string;
  'aria-describedby'?: string;
  role?: string;
}

export interface EnhancedoutletData extends outletDataItem {
  achievementRank?: number;
}

export interface OverallRevenueParams {
  period: string;
  includeForecasts: boolean;
}
