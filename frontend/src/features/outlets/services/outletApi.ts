import { apiClient } from '@/lib/api/client';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import {
  Outlet,
  CreateOutletRequest,
  UpdateOutletRequest,
  OutletFilters,
  OutletsListResponse,
  OutletDetails,
  OutletStats,
} from '../types';

export const outletApi = {
  // Get all outlets with filters
  getOutlets: async (
    filters?: OutletFilters & { page?: number; limit?: number }
  ): Promise<OutletsListResponse> => {
    const response = await apiClient.get(API_ENDPOINTS.OUTLETS.LIST, {
      params: filters,
    });
    return response.data;
  },

  // Get all outlets (simplified method for backward compatibility)
  getAllOutlets: async (): Promise<Outlet[]> => {
    const response = await apiClient.get(API_ENDPOINTS.OUTLETS.LIST, {
      params: { isActive: true, limit: 1000 }, // Get all active outlets
    });
    return response.data.outlets || response.data;
  },

  // Get single outlet with details
  getOutlet: async (id: string): Promise<OutletDetails> => {
    const response = await apiClient.get(API_ENDPOINTS.OUTLETS.GET(id));
    return response.data;
  },

  // Create new outlet
  createOutlet: async (data: CreateOutletRequest): Promise<Outlet> => {
    const response = await apiClient.post(API_ENDPOINTS.OUTLETS.CREATE, data);
    return response.data;
  },

  // Update outlet
  updateOutlet: async (id: string, data: UpdateOutletRequest): Promise<Outlet> => {
    const response = await apiClient.put(API_ENDPOINTS.OUTLETS.UPDATE(id), data);
    return response.data;
  },

  // Delete outlet (soft delete)
  deleteOutlet: async (id: string): Promise<void> => {
    await apiClient.delete(API_ENDPOINTS.OUTLETS.DELETE(id));
  },

  // Get outlet statistics
  getOutletStats: async (id: string, period?: string): Promise<OutletStats> => {
    const response = await apiClient.get(`${API_ENDPOINTS.OUTLETS.GET(id)}/stats`, {
      params: { period },
    });
    return response.data;
  },

  // Get outlet sales
  getOutletSales: async (id: string, filters?: any): Promise<any> => {
    const response = await apiClient.get(`${API_ENDPOINTS.OUTLETS.GET(id)}/sales`, {
      params: filters,
    });
    return response.data;
  },

  // Bulk operations
  bulkDeleteOutlets: async (ids: string[]): Promise<void> => {
    await apiClient.delete(`${API_ENDPOINTS.OUTLETS.LIST}/bulk`, {
      data: { ids },
    });
  },

  bulkUpdateOutlets: async (
    updates: Array<{ id: string; data: UpdateOutletRequest }>
  ): Promise<Outlet[]> => {
    const response = await apiClient.put(`${API_ENDPOINTS.OUTLETS.LIST}/bulk`, { updates });
    return response.data;
  },

  // Outlet activation/deactivation
  activateOutlet: async (id: string): Promise<Outlet> => {
    const response = await apiClient.post(`${API_ENDPOINTS.OUTLETS.UPDATE(id)}/activate`);
    return response.data;
  },

  deactivateOutlet: async (id: string): Promise<Outlet> => {
    const response = await apiClient.post(`${API_ENDPOINTS.OUTLETS.UPDATE(id)}/deactivate`);
    return response.data;
  },

  // Manager assignment
  assignManager: async (outletId: string, managerId: string): Promise<Outlet> => {
    const response = await apiClient.post(
      `${API_ENDPOINTS.OUTLETS.UPDATE(outletId)}/assign-manager`,
      {
        managerId,
      }
    );
    return response.data;
  },

  removeManager: async (outletId: string): Promise<Outlet> => {
    const response = await apiClient.post(
      `${API_ENDPOINTS.OUTLETS.UPDATE(outletId)}/remove-manager`
    );
    return response.data;
  },

  // Export outlets
  exportOutlets: async (filters?: OutletFilters): Promise<Blob> => {
    const response = await apiClient.get(`${API_ENDPOINTS.OUTLETS.LIST}/export`, {
      params: filters,
      responseType: 'blob',
    });
    return response.data;
  },

  // Import outlets
  importOutlets: async (file: File): Promise<{ success: number; errors: any[] }> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await apiClient.post(`${API_ENDPOINTS.OUTLETS.CREATE}/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Get outlet statistics summary
  getOutletStatsSummary: async (): Promise<{
    totalOutlets: number;
    activeOutlets: number;
    totalRevenue: number;
    averageRevenue: number;
  }> => {
    const response = await apiClient.get(`${API_ENDPOINTS.OUTLETS.LIST}/stats`);
    return response.data;
  },

  // Search outlets by location
  searchByLocation: async (
    latitude: number,
    longitude: number,
    radius: number
  ): Promise<Outlet[]> => {
    const response = await apiClient.get(`${API_ENDPOINTS.OUTLETS.LIST}/search-location`, {
      params: { latitude, longitude, radius },
    });
    return response.data;
  },
};
