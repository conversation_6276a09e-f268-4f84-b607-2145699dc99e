import { useQuery } from '@tanstack/react-query';
import { outletApi } from '../services/outletApi';
import { QUERY_KEYS } from '@/shared/utils/constants';
import { outletFilters } from '../types';

// Query Keys
export const outlet_QUERY_KEYS = {
  all: [QUERY_KEYS.outletS] as const,
  lists: () => [...outlet_QUERY_KEYS.all, 'list'] as const,
  list: (filters?: outletFilters) => [...outlet_QUERY_KEYS.lists(), filters] as const,
  details: () => [...outlet_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...outlet_QUERY_KEYS.details(), id] as const,
  stats: () => [...outlet_QUERY_KEYS.all, 'stats'] as const,
} as const;

// Get all outlets query
export function useoutlets(filters?: outletFilters) {
  return useQuery({
    queryKey: outlet_QUERY_KEYS.list(filters),
    queryFn: () => outletApi.getoutlets(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get single outlet query
export function useoutlet(id: string) {
  return useQuery({
    queryKey: outlet_QUERY_KEYS.detail(id),
    queryFn: () => outletApi.getoutlet(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Get outlet by user's outletId (for header display)
export function useUseroutlet(outletId: number | null) {
  return useQuery({
    queryKey: outlet_QUERY_KEYS.detail(outletId?.toString() || ''),
    queryFn: () => outletApi.getoutlet(outletId!.toString()),
    enabled: !!outletId,
    staleTime: 10 * 60 * 1000, // 10 minutes - outlet info doesn't change often
    gcTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Get active outlets only
export function useActiveoutlets() {
  return useQuery({
    queryKey: outlet_QUERY_KEYS.list({ isActive: true }),
    queryFn: () => outletApi.getoutlets({ isActive: true }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
