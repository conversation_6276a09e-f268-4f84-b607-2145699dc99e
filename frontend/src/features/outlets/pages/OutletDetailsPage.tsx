import { useParams } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';

export function OutletDetailsPage() {
  const { id } = useParams();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Outlet Details</h1>
        <p className="text-muted-foreground">View and edit outlet information.</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Outlet ID: {id}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center text-muted-foreground">
            Outlet details functionality will be implemented here.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
