import { But<PERSON> } from '@/shared/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Plus } from 'lucide-react';

export function OutletsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Outlet Management</h1>
          <p className="text-muted-foreground">Manage outlets and their information.</p>
        </div>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Add Outlet
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Outlets</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center text-muted-foreground">
            Outlet management functionality will be implemented here.
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
