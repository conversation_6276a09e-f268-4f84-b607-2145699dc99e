import { z } from 'zod';
import {
  nameSchema,
  phoneSchema,
  addressSchema,
  emailSchema,
  idSchema,
  paginationSchema,
} from '@/shared/validations/baseSchemas';

export const outletFormSchema = z.object({
  name: nameSchema,
  address: addressSchema,
  phone: phoneSchema,
  email: emailSchema.optional().or(z.literal('')),
  managerId: idSchema.optional().or(z.literal('')),
});

export const outletUpdateSchema = z.object({
  name: nameSchema.optional(),
  address: addressSchema.optional(),
  phone: phoneSchema.optional(),
  email: emailSchema.optional().or(z.literal('')),
  managerId: idSchema.optional().or(z.literal('')),
  isActive: z.boolean().optional(),
});

export const outletFiltersSchema = z
  .object({
    search: z.string().optional(),
    managerId: idSchema.optional(),
    isActive: z.boolean().optional(),
    dateFrom: z.string().optional(),
    dateTo: z.string().optional(),
  })
  .merge(paginationSchema);

export const outletLocationSchema = z.object({
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  address: addressSchema,
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  zipCode: z.string().min(1, 'ZIP code is required'),
  country: z.string().min(1, 'Country is required'),
});

export const bulkoutletImportSchema = z.object({
  outlets: z.array(outletFormSchema).min(1, 'At least one outlet is required'),
});

export const outletSearchLocationSchema = z.object({
  latitude: z.number().min(-90).max(90),
  longitude: z.number().min(-180).max(180),
  radius: z.number().min(1).max(100), // in kilometers
});

// Type exports
export type outletFormData = z.infer<typeof outletFormSchema>;
export type outletUpdateData = z.infer<typeof outletUpdateSchema>;
export type outletFiltersData = z.infer<typeof outletFiltersSchema>;
export type outletLocationData = z.infer<typeof outletLocationSchema>;
export type BulkoutletImportData = z.infer<typeof bulkoutletImportSchema>;
export type outletSearchLocationData = z.infer<typeof outletSearchLocationSchema>;
