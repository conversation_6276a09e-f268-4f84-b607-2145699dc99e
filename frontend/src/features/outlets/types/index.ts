import { User, Outlet as BaseOutlet } from '@/shared/types';

export interface Outlet extends BaseOutlet {}

export interface CreateOutletRequest {
  name: string;
  address: string;
  phone: string;
  email?: string;
  managerId?: string;
}

export interface UpdateOutletRequest {
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
  managerId?: string;
  isActive?: boolean;
}

export interface OutletFilters {
  search?: string;
  managerId?: string;
  isActive?: boolean;
  dateFrom?: string;
  dateTo?: string;
}

export interface OutletWithManager extends Outlet {
  manager?: User;
}

export interface OutletStats {
  totalSales: number;
  totalRevenue: number;
  averageOrderValue: number;
  salesCount: number;
  lastSaleDate?: string;
  monthlyGrowth: number;
}

export interface OutletWithStats extends OutletWithManager {
  stats: OutletStats;
}

export interface OutletsListResponse {
  outlets: OutletWithManager[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface OutletLocation {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface OutletDetails extends OutletWithManager {
  location?: OutletLocation;
  stats: OutletStats;
  recentSales: Array<{
    id: string;
    amount: number;
    date: string;
    user: User;
  }>;
}
