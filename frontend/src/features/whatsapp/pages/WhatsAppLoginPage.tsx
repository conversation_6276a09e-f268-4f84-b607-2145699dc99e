import { QRCodeDisplay, LoginInstructions } from '../components';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { MessageCircle } from 'lucide-react';
import { useWhatsAppStatus } from '../hooks/useWhatsAppStatus';
import WhatsAppStatusPage from './WhatsAppStatusPage';

const WhatsAppLoginPage = () => {
  const { status, isLoading } = useWhatsAppStatus();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <p>Loading WhatsApp status...</p>
      </div>
    );
  }

  if (status?.isConnected) {
    return <WhatsAppStatusPage />;
  }

  return (
    <div className="space-y-6 md:space-y-8">
      <div className="space-y-2 md:space-y-3">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">WhatsApp Login</h1>
        <p className="text-sm text-muted-foreground md:text-base">
          Scan the QR code to connect your WhatsApp account.
        </p>
      </div>
      <Card className="border-muted/40">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MessageCircle className="w-5 h-5 text-primary" />
            <span>Scan QR Code</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center">
          <QRCodeDisplay />
          <LoginInstructions />
        </CardContent>
      </Card>
    </div>
  );
};

export default WhatsAppLoginPage;
