import { ActiveConnectionDisplay } from '../components/ActiveConnectionDisplay';
import { useWhatsAppStatus } from '../hooks/useWhatsAppStatus';

const WhatsAppStatusPage = () => {
  const { isDisconnecting, disconnect } = useWhatsAppStatus();

  return (
    <div className="flex flex-col items-center justify-center space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold tracking-tight">WhatsApp Status</h1>
        <p className="mt-2 text-muted-foreground">
          Your WhatsApp account is currently connected.
        </p>
      </div>
      <ActiveConnectionDisplay onDisconnect={disconnect} isDisconnecting={isDisconnecting} />
    </div>
  );
};

export default WhatsAppStatusPage;