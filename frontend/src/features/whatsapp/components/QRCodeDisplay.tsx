import { useState, useEffect } from 'react';
import { Button } from '@/shared/components/ui/button';
import { API_ENDPOINTS } from '@/shared/utils/constants';
import { env } from '@/lib/config/env';
import { Loader2 } from 'lucide-react';

export const QRCodeDisplay = () => {
  const [timestamp, setTimestamp] = useState(Date.now());
  const [isQrLoading, setIsQrLoading] = useState(true);

  const refreshQrCode = () => {
    setIsQrLoading(true);
    setTimestamp(Date.now());
  };

  useEffect(() => {
    const intervalId = setInterval(refreshQrCode, 45000); // Refresh every 45 seconds
    return () => clearInterval(intervalId);
  }, []);

  return (
    <div className="flex flex-col items-center">
      <div className="relative w-64 h-64">
        {isQrLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-200 rounded-md">
            <Loader2 className="w-12 h-12 text-gray-500 animate-spin" />
          </div>
        )}
        <img
          src={`${env.API_BASE_URL}${API_ENDPOINTS.WHATSAPP.QR}?_t=${timestamp}`}
          alt="WhatsApp QR Code"
          className={`w-64 h-64 transition-opacity duration-300 ${isQrLoading ? 'opacity-0' : 'opacity-100'}`}
          onLoad={() => setIsQrLoading(false)}
        />
      </div>
      <Button onClick={refreshQrCode} className="mt-4">
        Refresh QR Code
      </Button>
    </div>
  );
};
