import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';

type ActiveConnectionDisplayProps = {
  onDisconnect: () => void;
  isDisconnecting: boolean;
};

export const ActiveConnectionDisplay = ({
  onDisconnect,
  isDisconnecting,
}: ActiveConnectionDisplayProps) => (
  <div className="flex w-full max-w-sm flex-col items-center rounded-lg border-2 border-dashed border-green-500/50 bg-green-500/10 p-8">
    <div className="mb-4 flex items-center space-x-3">
      <Badge
        variant="default"
        className="rounded-full bg-green-600 px-3 py-1 text-sm text-white hover:bg-green-700"
      >
        Active
      </Badge>
      <p className="text-lg font-semibold text-green-800">WhatsApp Connected</p>
    </div>
    <p className="mb-6 text-center text-gray-600">
      Your WhatsApp account is currently connected. You can now receive and send messages.
    </p>
    <Button
      onClick={onDisconnect}
      disabled={isDisconnecting}
      className="w-full transform rounded bg-red-600 px-4 py-2 font-bold text-white transition duration-300 ease-in-out hover:scale-105 hover:bg-red-700"
    >
      {isDisconnecting ? 'Disconnecting...' : 'Disconnect WhatsApp'}
    </Button>
  </div>
);
