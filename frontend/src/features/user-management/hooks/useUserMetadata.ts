import { useQuery } from '@tanstack/react-query';
import { userApi } from '../services/userApi';
import { UserRole, Department, outlet } from '../types';

export function useUserMetadata() {
  const {
    data: metadata,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['user-metadata'],
    queryFn: userApi.getMetadata,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const roles: UserRole[] = Array.isArray(metadata?.data?.roles) ? metadata.data.roles : [];
  const departments: Department[] = Array.isArray(metadata?.data?.departments)
    ? metadata.data.departments
    : [];
  const outlets: outlet[] = Array.isArray(metadata?.data?.outlets)
    ? metadata.data.outlets
    : [];

  return { roles, departments, outlets, isLoading, error };
}

export function useUserStats(userId?: number) {
  const { data, isLoading, error } = useQuery({
    queryKey: ['user-stats', userId],
    queryFn: () => userApi.getStats(userId),
    staleTime: 1000 * 60 * 1, // 1 minute
  });

  return { stats: data, isLoading, error };
}
