import React, { useState, useRef } from 'react';
import {
  Upload,
  Download,
  FileText,
  AlertCircle,
  CheckCircle,
  ChevronDown,
  ChevronUp,
} from 'lucide-react';
import { Button } from '@/shared/components/ui/button';
import { Input } from '@/shared/components/ui/input';
import { Label } from '@/shared/components/ui/label';
import { Progress } from '@/shared/components/ui/progress';
import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/shared/components/ui/collapsible';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select';
import { Checkbox } from '@/shared/components/ui/checkbox';

interface UserImportExportProps {
  onImportComplete: () => void;
  onExportComplete?: () => void;
}

interface ImportResult {
  success: boolean;
  message: string;
  imported: number;
  failed: number;
  errors?: string[];
}

const UserImportExport: React.FC<UserImportExportProps> = ({
  onImportComplete,
  onExportComplete,
}) => {
  const [activeTab, setActiveTab] = useState('import');
  const [selectedFile, setSelectedFile] = useState<any>(null);
  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [exporting, setExporting] = useState(false);
  const [exportFormat, setExportFormat] = useState<'csv' | 'xlsx'>('csv');
  const [includeInactive, setIncludeInactive] = useState(false);
  const [showImportHelp, setShowImportHelp] = useState(false);
  const [showExportOptions, setShowExportOptions] = useState(false);
  const fileInputRef = useRef<any>(null);

  const handleFileSelect = (event: any) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
        setSelectedFile(file);
        setImportResult(null);
      } else {
        alert('Please select a CSV file');
      }
    }
  };

  const parseCSV = (csvText: string): any[] => {
    const lines = csvText.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
    const users = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
      if (values.length === headers.length) {
        const user: any = {};
        headers.forEach((header, index) => {
          user[header.toLowerCase().replace(/\s+/g, '')] = values[index];
        });
        users.push(user);
      }
    }

    return users;
  };

  const validateUserData = (users: any[]): { valid: any[]; errors: string[] } => {
    const valid: any[] = [];
    const errors: string[] = [];

    users.forEach((user, index) => {
      const rowNum = index + 2; // +2 because index starts at 0 and we skip header

      if (!user.email || !user.email.includes('@')) {
        errors.push(`Row ${rowNum}: Invalid email format`);
        return;
      }

      if (!user.name && !user.fullname) {
        errors.push(`Row ${rowNum}: Missing required field: name`);
        return;
      }

      if (!user.role) {
        errors.push(`Row ${rowNum}: Missing required field: role`);
        return;
      }

      valid.push({
        email: user.email,
        fullName: user.name || user.fullname,
        role: user.role.toLowerCase(),
        department: user.department || '',
        outletId: user.outletid ? parseInt(user.outletid) : undefined,
        isActive: user.active !== 'false',
      });
    });

    return { valid, errors };
  };

  const handleExport = async () => {
    setExporting(true);

    try {
      // Simulate API call to get user data
      await new Promise(resolve => setTimeout(resolve, 1000));

      const mockUserData = [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          role: 'user',
          department: 'IT',
          outletId: '',
          active: 'true',
          createdAt: '2024-01-15',
        },
        {
          name: 'Jane Smith',
          email: '<EMAIL>',
          role: 'manager',
          department: 'HR',
          outletId: '',
          active: 'true',
          createdAt: '2024-01-16',
        },
        {
          name: 'Bob Johnson',
          email: '<EMAIL>',
          role: 'user',
          department: 'Operations',
          outletId: '1',
          active: 'false',
          createdAt: '2024-01-10',
        },
      ];

      // Filter data based on options
      const filteredData = includeInactive
        ? mockUserData
        : mockUserData.filter(user => user.active === 'true');

      // Generate export content
      const headers = 'Name,Email,Role,Department,outlet ID,Active,Created At';
      const csvContent = [
        headers,
        ...filteredData.map(
          user =>
            `${user.name},${user.email},${user.role},${user.department},${user.outletId},${user.active},${user.createdAt}`
        ),
      ].join('\n');

      // Create and download file
      const mimeType =
        exportFormat === 'csv'
          ? 'text/csv'
          : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      const blob = new Blob([csvContent], { type: mimeType });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `users-export-${new Date().toISOString().split('T')[0]}.${exportFormat}`;
      link.click();
      URL.revokeObjectURL(url);

      if (onExportComplete) {
        onExportComplete();
      }
    } catch (error) {
      console.error('Export error:', error);
      alert('Export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    setImporting(true);
    setImportProgress(0);
    setImportResult(null);

    try {
      // Read file content
      const fileContent = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = e => resolve(e.target?.result as string);
        reader.onerror = reject;
        reader.readAsText(selectedFile);
      });

      setImportProgress(25);

      // Parse CSV
      const parsedUsers = parseCSV(fileContent);
      setImportProgress(50);

      // Validate data
      const { valid, errors } = validateUserData(parsedUsers);
      setImportProgress(75);

      // Simulate API call for import
      await new Promise(resolve => setTimeout(resolve, 1000));
      setImportProgress(100);

      const result: ImportResult = {
        success: valid.length > 0,
        message: valid.length > 0 ? 'Import completed successfully' : 'No valid users to import',
        imported: valid.length,
        failed: errors.length,
        errors: errors,
      };

      setImportResult(result);

      if (result.success && result.imported > 0) {
        setTimeout(() => {
          onImportComplete();
        }, 2000);
      }
    } catch (error) {
      console.error('Import error:', error);
      setImportResult({
        success: false,
        message: 'Import failed',
        imported: 0,
        failed: 0,
        errors: ['An unexpected error occurred during import. Please check your file format.'],
      });
    } finally {
      setImporting(false);
    }
  };

  const downloadTemplate = () => {
    const csvContent = `Name,Email,Role,Department,outlet ID,Active
John Doe,<EMAIL>,user,IT,,true
Jane Smith,<EMAIL>,manager,HR,,true
Bob Johnson,<EMAIL>,outlet_manager,Operations,1,true`;

    const blob = new (globalThis as any).Blob([csvContent], { type: 'text/csv' });
    const url = (globalThis as any).URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'user-import-template.csv';
    link.click();
    (globalThis as any).URL.revokeObjectURL(url);
  };

  const downloadSampleData = () => {
    const csvContent = `Name,Email,Role,Department,outlet ID,Active
Alice Cooper,<EMAIL>,user,Marketing,,true
Charlie Brown,<EMAIL>,admin,IT,,true
Diana Prince,<EMAIL>,outlet_manager,Operations,2,true
Edward Norton,<EMAIL>,finance_manager,Finance,,true
Fiona Green,<EMAIL>,hr_manager,HR,,false`;

    const blob = new (globalThis as any).Blob([csvContent], { type: 'text/csv' });
    const url = (globalThis as any).URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'sample-users.csv';
    link.click();
    (globalThis as any).URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <p className="text-muted-foreground text-sm leading-relaxed">
          Bulk import users from CSV or export current users
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid h-11 w-full grid-cols-2">
          <TabsTrigger value="import" className="flex items-center gap-2 text-sm font-medium">
            <Upload className="h-4 w-4" />
            Import Users
          </TabsTrigger>
          <TabsTrigger value="export" className="flex items-center gap-2 text-sm font-medium">
            <Download className="h-4 w-4" />
            Export Users
          </TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="space-y-6">
          {/* Primary Import Action */}
          <div className="space-y-4">
            <div className="space-y-3">
              <Label htmlFor="csvFile" className="text-base font-medium">
                Select CSV File to Import
              </Label>
              <Input
                id="csvFile"
                type="file"
                accept=".csv"
                onChange={handleFileSelect}
                ref={fileInputRef}
                disabled={importing}
                className="cursor-pointer"
              />
            </div>

            {selectedFile && (
              <div className="bg-muted/50 border-muted flex items-center gap-3 rounded-lg border p-4">
                <div className="bg-background rounded-md p-2">
                  <FileText className="text-muted-foreground h-5 w-5" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="truncate text-sm font-medium">{selectedFile.name}</p>
                  <p className="text-muted-foreground text-xs">
                    {(selectedFile.size / 1024).toFixed(1)} KB • CSV file
                  </p>
                </div>
              </div>
            )}

            {/* Primary Import Button */}
            <Button
              onClick={handleImport}
              disabled={!selectedFile || importing}
              className="h-11 w-full text-base"
              size="lg"
            >
              <Upload className="mr-2 h-5 w-5" />
              {importing ? 'Importing Users...' : 'Import Users'}
            </Button>
          </div>

          {/* Collapsible Help Section */}
          <Collapsible open={showImportHelp} onOpenChange={setShowImportHelp}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="h-auto w-full justify-between p-0">
                <span className="text-muted-foreground text-sm">
                  Need help? View import instructions and download templates
                </span>
                {showImportHelp ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4">
              <div className="bg-muted/50 space-y-3 rounded-lg p-4">
                <div className="space-y-2 text-sm">
                  <p className="font-medium">Import Steps:</p>
                  <ol className="text-muted-foreground list-inside list-decimal space-y-1">
                    <li>Download the CSV template or use sample data</li>
                    <li>Fill in user information following the template format</li>
                    <li>Upload the completed CSV file above</li>
                    <li>Review and confirm the import</li>
                  </ol>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={downloadTemplate}>
                    <Download className="mr-2 h-4 w-4" />
                    Download Template
                  </Button>
                  <Button variant="outline" size="sm" onClick={downloadSampleData}>
                    <Download className="mr-2 h-4 w-4" />
                    Sample Data
                  </Button>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Progress and Results */}
          {importing && (
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="font-medium">Importing users...</span>
                <span className="text-muted-foreground">{importProgress}%</span>
              </div>
              <Progress value={importProgress} className="h-2" />
            </div>
          )}

          {importResult && (
            <Alert
              className={
                importResult.success
                  ? 'border-green-500 bg-green-50'
                  : 'border-destructive bg-destructive/5'
              }
            >
              <div className="flex items-start gap-3">
                {importResult.success ? (
                  <CheckCircle className="mt-0.5 h-5 w-5 text-green-600" />
                ) : (
                  <AlertCircle className="text-destructive mt-0.5 h-5 w-5" />
                )}
                <AlertDescription className="flex-1">
                  <div className="space-y-3">
                    <p className="font-medium">{importResult.message}</p>
                    {importResult.success && (
                      <div className="flex gap-4 text-sm">
                        <span className="font-medium text-green-600">
                          ✓ {importResult.imported} imported
                        </span>
                        {importResult.failed > 0 && (
                          <span className="font-medium text-yellow-600">
                            ⚠ {importResult.failed} failed
                          </span>
                        )}
                      </div>
                    )}
                    {importResult.errors && importResult.errors.length > 0 && (
                      <div>
                        <p className="mb-2 text-sm font-medium">Errors:</p>
                        <ul className="text-muted-foreground list-inside list-disc space-y-1 text-sm">
                          {importResult.errors.map((error, index) => (
                            <li key={index}>{error}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </AlertDescription>
              </div>
            </Alert>
          )}
        </TabsContent>

        <TabsContent value="export" className="space-y-6">
          {/* Primary Export Action */}
          <div className="space-y-4">
            <div className="space-y-2 text-center">
              <p className="text-muted-foreground text-sm">
                Export user data for backup or migration purposes
              </p>
            </div>

            {/* Primary Export Button */}
            <Button
              onClick={handleExport}
              disabled={exporting}
              className="h-11 w-full text-base"
              size="lg"
            >
              <Download className="mr-2 h-5 w-5" />
              {exporting ? 'Exporting Users...' : `Export Users (${exportFormat.toUpperCase()})`}
            </Button>
          </div>

          {/* Collapsible Export Options */}
          <Collapsible open={showExportOptions} onOpenChange={setShowExportOptions}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="h-auto w-full justify-between p-0">
                <span className="text-muted-foreground text-sm">
                  Export options (format, filters)
                </span>
                {showExportOptions ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4">
              <div className="bg-muted/50 space-y-4 rounded-lg p-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="exportFormat">Export Format</Label>
                    <Select
                      value={exportFormat}
                      onValueChange={(value: 'csv' | 'xlsx') => setExportFormat(value)}
                    >
                      <SelectTrigger id="exportFormat">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="csv">CSV</SelectItem>
                        <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Options</Label>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="includeInactive"
                        checked={includeInactive}
                        onCheckedChange={checked => setIncludeInactive(!!checked)}
                      />
                      <Label htmlFor="includeInactive" className="text-sm font-normal">
                        Include inactive users
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Simplified Export Info */}
          <div className="text-center">
            <p className="text-muted-foreground text-xs">
              Export includes: Name, Email, Role, Department, outlet ID, Status, Created Date
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default UserImportExport;
