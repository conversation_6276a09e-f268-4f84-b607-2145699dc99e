import { apiClient } from '@/lib/api/client';
import {
  User,
  CreateUserRequest,
  UpdateUserRequest,
  UserFilters,
  UsersResponse,
  BulkUserOperation,
  PasswordResetRequest,
  UserAuditLog,
  Department,
  UserRole,
  outlet,
  PaginationConfig,
} from '../types';

export class UserManagementService {
  private static baseUrl = '/users';

  /**
   * Get all users with filtering, sorting, and pagination
   */
  static async getUsers(
    params: {
      page?: number;
      limit?: number;
      filters?: UserFilters;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<UsersResponse> {
    try {
      const response = await apiClient.get(this.baseUrl, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Get a single user by ID
   */
  static async getUser(userId: number): Promise<User> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  }

  /**
   * Create a new user
   */
  static async createUser(userData: CreateUserRequest): Promise<User> {
    try {
      const response = await apiClient.post(this.baseUrl, userData);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update an existing user
   */
  static async updateUser(userId: number, userData: UpdateUserRequest): Promise<User> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Delete a user
   */
  static async deleteUser(userId: number): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/${userId}`);
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Toggle user active status
   */
  static async toggleUserStatus(userId: number, isActive: boolean): Promise<User> {
    try {
      const response = await apiClient.patch(`${this.baseUrl}/${userId}/status`, {
        isActive,
      });
      return response.data;
    } catch (error) {
      console.error('Error toggling user status:', error);
      throw error;
    }
  }

  /**
   * Reset user password
   */
  static async resetPassword(
    userId: number,
    options: Omit<PasswordResetRequest, 'userId'> = {}
  ): Promise<{
    success: boolean;
    temporaryPassword?: string;
    message: string;
  }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/${userId}/reset-password`, options);
      return response.data;
    } catch (error) {
      console.error('Error resetting password:', error);
      throw error;
    }
  }

  /**
   * Perform bulk operations on users
   */
  static async bulkOperation(operation: BulkUserOperation): Promise<{
    success: boolean;
    affectedUsers: number;
    message: string;
  }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/bulk`, operation);
      return response.data;
    } catch (error) {
      console.error('Error performing bulk operation:', error);
      throw error;
    }
  }

  /**
   * Check if email exists
   */
  static async checkEmailExists(email: string): Promise<{ exists: boolean }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/email/${encodeURIComponent(email)}`);
      return response.data;
    } catch (error) {
      console.error('Error checking email:', error);
      throw error;
    }
  }

  /**
   * Get user statistics
   */
  static async getUserStats(): Promise<{
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    newUsersThisMonth: number;
    usersByRole: Record<string, number>;
    usersByDepartment: Record<string, number>;
  }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw error;
    }
  }

  /**
   * Get user audit logs
   */
  static async getUserAuditLogs(
    params: {
      userId?: number;
      limit?: number;
      page?: number;
    } = {}
  ): Promise<{
    logs: UserAuditLog[];
    pagination: PaginationConfig;
  }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/audit-logs`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      throw error;
    }
  }

  /**
   * Get available user roles
   */
  static async getUserRoles(): Promise<UserRole[]> {
    try {
      const response = await apiClient.get('/admin/roles');
      return response.data;
    } catch (error) {
      console.error('Error fetching user roles:', error);
      // Return default roles if API fails
      return [
        { value: 'user', label: 'User', permissions: ['read'] },
        { value: 'staff', label: 'Staff', permissions: ['read', 'write'] },
        { value: 'admin', label: 'Admin', permissions: ['read', 'write', 'delete'] },
      ];
    }
  }

  /**
   * Get available departments
   */
  static async getDepartments(): Promise<Department[]> {
    try {
      const response = await apiClient.get('/admin/departments');
      return response.data;
    } catch (error) {
      console.error('Error fetching departments:', error);
      // Return default departments if API fails
      return [
        { id: 1, name: 'Operations' },
        { id: 2, name: 'Finance' },
        { id: 3, name: 'Marketing' },
        { id: 4, name: 'Human Resources' },
        { id: 5, name: 'Information Technology' },
      ];
    }
  }

  /**
   * Get available outlets
   */
  static async getoutlets(): Promise<outlet[]> {
    try {
      const response = await apiClient.get('/outlets');
      return response.data;
    } catch (error) {
      console.error('Error fetching outlets:', error);
      return [];
    }
  }

  /**
   * Export users data
   */
  static async exportUsers(format: 'csv' | 'xlsx' = 'csv', filters?: UserFilters): Promise<any> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/export`, {
        params: { format, ...filters },
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting users:', error);
      throw error;
    }
  }

  /**
   * Import users from file
   */
  static async importUsers(file: any): Promise<{
    success: boolean;
    imported: number;
    errors: Array<{ row: number; message: string }>;
  }> {
    try {
      const formData = new (globalThis as any).FormData();
      formData.append('file', file);

      const response = await apiClient.post(`${this.baseUrl}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error importing users:', error);
      throw error;
    }
  }
}

export default UserManagementService;
