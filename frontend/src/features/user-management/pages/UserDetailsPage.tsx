import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { ArrowLeft, Edit, Trash2, UserCheck, UserX, Key, Activity } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';

import { Alert, AlertDescription } from '@/shared/components/ui/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/ui/dialog';
import { LoadingSpinner } from '@/shared/components/ui/loading-spinner';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs';
import {
  useUser,
  useUpdateUser,
  useDeleteUser,
  useToggleUserStatus,
  useResetPassword,
  useUserMetadata,
} from '../hooks';
import { UpdateUserRequest } from '../types';
import { useUserAuditLogs } from '@/features/audit-logs/hooks';
import { UserForm, UserDeleteDialog, UserPasswordReset } from '../components';
import { UserAuditLog } from '@/features/audit-logs/components';
import { formatDate } from '../utils';

export function UserDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // State for dialogs
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showPasswordResetDialog, setShowPasswordResetDialog] = useState(false);
  const [alert, setAlert] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  // Queries and mutations
  const { data: user, isLoading, error, refetch } = useUser(parseInt(id || '0'));
  const {
    data: auditLogsData,
    isLoading: auditLoading,
    refetch: refetchAuditLogs,
  } = useUserAuditLogs(user?.userId || 0);

  const auditLogs = auditLogsData?.logs || [];
  const { outlets } = useUserMetadata();

  const updateUserMutation = useUpdateUser();
  const deleteUserMutation = useDeleteUser();
  const toggleStatusMutation = useToggleUserStatus();
  const resetPasswordMutation = useResetPassword();

  // Clear alert after 5 seconds
  React.useEffect(() => {
    if (alert) {
      const timer = setTimeout(() => setAlert(null), 5000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [alert]);

  if (!id) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertDescription>Invalid user ID provided.</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertDescription>
            {error?.message ||
              'User not found. The user may have been deleted or you may not have permission to view it.'}
          </AlertDescription>
        </Alert>
        <Button variant="outline" onClick={() => navigate('/users')}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Users
        </Button>
      </div>
    );
  }

  // Event handlers
  const handleUpdateUser = async (userData: any) => {
    try {
      // Filter out any undefined values and ensure we only send valid update fields
      const updateData: UpdateUserRequest = {
        ...(userData.username && { username: userData.username }),
        ...(userData.email && { email: userData.email }),
        ...(userData.fullName && { fullName: userData.fullName }),
        ...(userData.outletId !== undefined && { outletId: userData.outletId }),
        ...(userData.isActive !== undefined && { isActive: userData.isActive }),
      };

      await updateUserMutation.mutateAsync({
        userId: parseInt(id || '0'),
        userData: updateData,
      });
      setShowEditDialog(false);
      setAlert({ type: 'success', message: 'User updated successfully' });
      refetch();
    } catch (error) {
      console.error('Failed to update user:', error);
      setAlert({ type: 'error', message: 'Failed to update user' });
    }
  };

  const handleDeleteUser = async () => {
    try {
      await deleteUserMutation.mutateAsync(parseInt(id || '0'));
      setShowDeleteDialog(false);
      setAlert({ type: 'success', message: 'User deleted successfully' });
      navigate('/users');
    } catch (error) {
      console.error('Failed to delete user:', error);
      setAlert({ type: 'error', message: 'Failed to delete user' });
    }
  };

  const handleToggleStatus = async () => {
    try {
      await toggleStatusMutation.mutateAsync({
        userId: user.userId || 0,
        isActive: !user.isActive,
      });
      setAlert({
        type: 'success',
        message: `User ${!user.isActive ? 'activated' : 'deactivated'} successfully`,
      });
      refetch();
    } catch (error) {
      console.error('Failed to toggle user status:', error);
      setAlert({ type: 'error', message: 'Failed to toggle user status' });
    }
  };

  const handlePasswordReset = async (options?: any) => {
    try {
      await resetPasswordMutation.mutateAsync({
        userId: user.userId || 0,
        options,
      });
      setShowPasswordResetDialog(false);
      setAlert({ type: 'success', message: 'Password reset successfully' });
    } catch (error) {
      console.error('Failed to reset password:', error);
      setAlert({ type: 'error', message: 'Failed to reset password' });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => navigate('/users')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Users
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{user.fullName || user.username}</h1>
            <p className="text-muted-foreground">{user.email}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setShowEditDialog(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleStatus}
            disabled={toggleStatusMutation.isPending}
          >
            {user.isActive ? (
              <>
                <UserX className="mr-2 h-4 w-4" />
                Deactivate
              </>
            ) : (
              <>
                <UserCheck className="mr-2 h-4 w-4" />
                Activate
              </>
            )}
          </Button>
          <Button variant="outline" size="sm" onClick={() => setShowPasswordResetDialog(true)}>
            <Key className="mr-2 h-4 w-4" />
            Reset Password
          </Button>
          <Button variant="destructive" size="sm" onClick={() => setShowDeleteDialog(true)}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      {/* Alert */}
      {alert && (
        <Alert className={alert.type === 'error' ? 'border-destructive' : 'border-green-500'}>
          <AlertDescription>{alert.message}</AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs defaultValue="details" className="space-y-4">
        <TabsList>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="activity">Activity Log</TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-4">
          {/* User Information */}
          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="text-muted-foreground text-sm font-medium">Username</label>
                  <p className="text-sm">{user.username || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-muted-foreground text-sm font-medium">Full Name</label>
                  <p className="text-sm">{user.fullName || 'N/A'}</p>
                </div>
                <div>
                  <label className="text-muted-foreground text-sm font-medium">Email</label>
                  <p className="text-sm">{user.email}</p>
                </div>
                <div>
                  <label className="text-muted-foreground text-sm font-medium">Role</label>
                  <Badge variant="secondary">{user.role}</Badge>
                </div>
                <div>
                  <label className="text-muted-foreground text-sm font-medium">Status</label>
                  <Badge variant={user.isActive ? 'default' : 'destructive'}>
                    {user.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>

                <div>
                  <label className="text-muted-foreground text-sm font-medium">outlet</label>
                  <p className="text-sm">
                    {user.outletId ? `outlet ${user.outletId}` : 'N/A'}
                  </p>
                </div>
                <div>
                  <label className="text-muted-foreground text-sm font-medium">Created</label>
                  <p className="text-sm">{user.createdAt ? formatDate(user.createdAt) : 'N/A'}</p>
                </div>
                <div>
                  <label className="text-muted-foreground text-sm font-medium">Last Updated</label>
                  <p className="text-sm">{user.updatedAt ? formatDate(user.updatedAt) : 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Activity Log
                </CardTitle>
                <Button variant="outline" size="sm" onClick={() => refetchAuditLogs()}>
                  Refresh
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <UserAuditLog
                userId={user.userId}
                auditLogs={auditLogs}
                loading={auditLoading}
                onRefresh={() => refetchAuditLogs()}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
          </DialogHeader>
          <UserForm
            user={user}
            isOpen={showEditDialog}
            onClose={() => setShowEditDialog(false)}
            onSubmit={handleUpdateUser}
            loading={updateUserMutation.isPending}
            outlets={outlets}
          />
        </DialogContent>
      </Dialog>

      <UserDeleteDialog
        user={user}
        open={showDeleteDialog}
        onConfirm={handleDeleteUser}
        onCancel={() => setShowDeleteDialog(false)}
      />

      <UserPasswordReset
        user={user}
        open={showPasswordResetDialog}
        onConfirm={handlePasswordReset}
        onCancel={() => setShowPasswordResetDialog(false)}
      />
    </div>
  );
}
