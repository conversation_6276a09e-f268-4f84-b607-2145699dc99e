import { User, UserFilters, UserSortConfig } from '../types';

/**
 * Format user role for display
 */
export const formatUserRole = (role: 'admin' | 'staff' | 'user'): string => {
  const roleMap: Record<string, string> = {
    user: 'User',
    staff: 'Staff',
    admin: 'Admin',
  };

  return roleMap[role] || role.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

/**
 * Format user status for display
 */
export const formatUserStatus = (
  user: User
): {
  label: string;
  color: string;
  variant: 'default' | 'secondary' | 'destructive' | 'outline';
} => {
  if (user.isActive === false) {
    return {
      label: 'Inactive',
      color: 'text-red-600',
      variant: 'destructive',
    };
  }

  if (user.requiresOnboarding) {
    return {
      label: 'Pending',
      color: 'text-yellow-600',
      variant: 'outline',
    };
  }

  return {
    label: 'Active',
    color: 'text-green-600',
    variant: 'default',
  };
};

/**
 * Get user initials for avatar
 */
export const getUserInitials = (user: User): string => {
  if (user.fullName) {
    return user.fullName
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  if (user.username) {
    return user.username.charAt(0).toUpperCase();
  }

  return user.email.charAt(0).toUpperCase();
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

/**
 * Format relative time (e.g., "2 hours ago")
 */
export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
  }

  const diffInYears = Math.floor(diffInMonths / 12);
  return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
};

/**
 * Filter users based on search criteria (matches backend implementation)
 */
export const filterUsers = (users: User[], filters: UserFilters): User[] => {
  return users.filter(user => {
    // Search filter - matches backend SQL LIKE queries
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase().trim();
      if (searchTerm) {
        // Backend searches username, email, and fullName with LIKE %term%
        const searchableFields = [user.username, user.email, user.fullName].filter(Boolean);

        const matchesSearch = searchableFields.some(field =>
          field?.toLowerCase().includes(searchTerm)
        );

        if (!matchesSearch) return false;
      }
    }

    // Role filter
    if (filters.role && filters.role !== 'all' && user.role !== filters.role) {
      return false;
    }

    // outlet filter
    if (filters.outletId && user.outletId !== filters.outletId) {
      return false;
    }

    return true;
  });
};

/**
 * Sort users based on sort configuration
 */
export const sortUsers = (users: User[], sortConfig: UserSortConfig): User[] => {
  return [...users].sort((a, b) => {
    const aValue = a[sortConfig.field];
    const bValue = b[sortConfig.field];

    // Handle null/undefined values
    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;
    if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;

    // Handle different data types
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const comparison = aValue.localeCompare(bValue);
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      const comparison = aValue - bValue;
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    }

    // Handle dates
    if (sortConfig.field === 'createdAt' || sortConfig.field === 'updatedAt') {
      const aDate = new Date(aValue as string).getTime();
      const bDate = new Date(bValue as string).getTime();
      const comparison = aDate - bDate;
      return sortConfig.direction === 'asc' ? comparison : -comparison;
    }

    // Default string comparison
    const comparison = String(aValue).localeCompare(String(bValue));
    return sortConfig.direction === 'asc' ? comparison : -comparison;
  });
};

/**
 * Generate a secure temporary password
 */
export const generateTemporaryPassword = (length: number = 12): string => {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
  let password = '';

  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }

  return password;
};

/**
 * Validate password strength
 */
export const validatePasswordStrength = (
  password: string
): {
  score: number;
  feedback: string[];
  isValid: boolean;
} => {
  const feedback: string[] = [];
  let score = 0;

  if (password.length < 8) {
    feedback.push('Password must be at least 8 characters long');
  } else {
    score += 1;
  }

  if (!/[a-z]/.test(password)) {
    feedback.push('Password must contain at least one lowercase letter');
  } else {
    score += 1;
  }

  if (!/[A-Z]/.test(password)) {
    feedback.push('Password must contain at least one uppercase letter');
  } else {
    score += 1;
  }

  if (!/\d/.test(password)) {
    feedback.push('Password must contain at least one number');
  } else {
    score += 1;
  }

  if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
    feedback.push('Password must contain at least one special character');
  } else {
    score += 1;
  }

  return {
    score,
    feedback,
    isValid: score >= 4 && password.length >= 8,
  };
};

/**
 * Export users data to CSV format
 */
export const exportUsersToCSV = (users: User[]): string => {
  const headers = [
    'ID',
    'Username',
    'Email',
    'Full Name',
    'Role',
    'outlet ID',
    'Status',
    'Created At',
    'Updated At',
    'Last Login',
  ];

  const csvContent = [
    headers.join(','),
    ...users.map(user =>
      [
        user.userId,
        user.username || '',
        user.email,
        user.fullName || '',
        user.role,
        user.outletId || '',
        user.isActive ? 'Active' : 'Inactive',
        user.createdAt || '',
        user.updatedAt || '',
        user.lastLoginAt || '',
      ]
        .map(field => `"${String(field).replace(/"/g, '""')}"`)
        .join(',')
    ),
  ].join('\n');

  return csvContent;
};

/**
 * Download data as file
 */
export const downloadFile = (
  content: string,
  filename: string,
  mimeType: string = 'text/plain'
): void => {
  const blob = new (globalThis as any).Blob([content], { type: mimeType });
  const url = (globalThis as any).URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  (globalThis as any).URL.revokeObjectURL(url);
};

/**
 * Get user permission level
 */
export const getUserPermissionLevel = (role: 'admin' | 'staff' | 'user'): number => {
  const permissionLevels: Record<string, number> = {
    user: 1,
    staff: 2,
    admin: 3,
  };

  return permissionLevels[role] || 0;
};

/**
 * Check if user can perform action on target user
 */
export const canUserPerformAction = (
  currentUser: User,
  targetUser: User,
  action: string
): boolean => {
  const currentUserLevel = getUserPermissionLevel(currentUser.role);
  const targetUserLevel = getUserPermissionLevel(targetUser.role);

  // Admins can do anything except to other admins
  if (currentUser.role === 'admin') {
    return targetUser.role !== 'admin' || currentUser.userId === targetUser.userId;
  }

  // Users can only edit themselves
  if (currentUser.role === 'user') {
    return currentUser.userId === targetUser.userId && action === 'edit';
  }

  // Staff can't modify users with higher or equal permission levels
  if (action === 'delete' || action === 'edit' || action === 'deactivate') {
    return currentUserLevel > targetUserLevel;
  }

  return false;
};

/**
 * Get available actions for a user
 */
export const getAvailableUserActions = (currentUser: User, targetUser: User): string[] => {
  const actions: string[] = [];

  if (canUserPerformAction(currentUser, targetUser, 'edit')) {
    actions.push('edit');
  }

  if (canUserPerformAction(currentUser, targetUser, 'delete')) {
    actions.push('delete');
  }

  if (canUserPerformAction(currentUser, targetUser, 'deactivate')) {
    actions.push(targetUser.isActive ? 'deactivate' : 'activate');
  }

  if (currentUser.role === 'admin') {
    actions.push('reset_password');
  }

  return actions;
};

/**
 * Format file size for display
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Debounce function for search inputs with cancellation support
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) & { cancel: () => void } => {
  let timeout: any;

  const debouncedFunction = (...args: Parameters<T>) => {
    (globalThis as any).clearTimeout(timeout);
    timeout = (globalThis as any).setTimeout(() => func(...args), wait);
  };

  debouncedFunction.cancel = () => {
    (globalThis as any).clearTimeout(timeout);
  };

  return debouncedFunction;
};
