import { User } from '../types';

/**
 * Check if user has a specific role
 */
export function hasRole(user: User | null, role: string): boolean {
  return user?.role === role;
}

/**
 * Check if user is admin
 */
export function isAdmin(user: User | null): boolean {
  return hasRole(user, 'admin');
}

/**
 * Check if user is regular user
 */
export function isUser(user: User | null): boolean {
  return hasRole(user, 'user');
}

/**
 * Check if user can access admin features
 */
export function canAccessAdmin(user: User | null): boolean {
  return isAdmin(user);
}

/**
 * Check if user can manage other users
 */
export function canManageUsers(user: User | null): boolean {
  return isAdmin(user);
}

/**
 * Get the default redirect path for a user based on their role
 */
export function getDefaultRedirectPath(user: User | null): string {
  if (!user) {
    return '/login';
  }

  switch (user.role) {
    case 'admin':
      return '/admin/dashboard';
    case 'staff':
      return '/dashboard';
    case 'user':
      return '/dashboard';
    default:
      return '/dashboard';
  }
}

/**
 * Check if user can manage outlets
 */
export function canManageoutlets(user: User | null): boolean {
  return isAdmin(user);
}

/**
 * Check if user can view all sales data
 */
export function canViewAllSales(user: User | null): boolean {
  return isAdmin(user);
}

/**
 * Get user display name
 */
export function getUserDisplayName(user: User | null): string {
  if (!user) return 'Guest';
  return user.name || user.fullName || user.email || 'Unknown User';
}

/**
 * Get user initials for avatar
 */
export function getUserInitials(user: User | null): string {
  const displayName = user?.name || user?.fullName;
  if (!displayName) return 'U';

  const names = displayName.split(' ');
  if (names.length === 1) {
    return names[0].charAt(0).toUpperCase();
  }

  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
}

/**
 * Check if user account is active
 */
export function isUserActive(user: User | null): boolean {
  return user?.isActive === true;
}

/**
 * Format last login time
 */
export function formatLastLogin(user: User | null): string {
  if (!user?.lastLoginAt) return 'Never';

  const lastLogin = new Date(user.lastLoginAt);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - lastLogin.getTime()) / (1000 * 60 * 60));

  if (diffInHours < 1) return 'Just now';
  if (diffInHours < 24) return `${diffInHours} hours ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays} days ago`;

  return lastLogin.toLocaleDateString();
}

/**
 * Validate password strength
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  score: number;
  feedback: string[];
} {
  const feedback: string[] = [];
  let score = 0;

  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Password must be at least 8 characters long');
  }

  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one lowercase letter');
  }

  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one uppercase letter');
  }

  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one number');
  }

  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Password must contain at least one special character');
  }

  return {
    isValid: score >= 4,
    score,
    feedback,
  };
}
