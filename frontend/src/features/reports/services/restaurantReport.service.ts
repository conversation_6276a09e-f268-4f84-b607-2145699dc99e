import { apiClient } from '@/lib/api/client';
import type {
  outletReportInput,
  outletReport,
  outletReportsResponse,
  outletReportFilters,
} from '../types';

const BASE_URL = '/sales/reports';

export const outletReportService = {
  /**
   * Create a new outlet report
   */
  async createReport(reportData: outletReportInput): Promise<outletReport> {
    const response: {
      success: boolean;
      message: string;
      data: outletReport;
    } = await apiClient.post(BASE_URL, reportData);

    if (!response.success) {
      // Check if the message indicates success despite success flag being false
      const message = response.message || 'Failed to create outlet report';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      // If message indicates success, log the inconsistency but don't throw
      console.warn('API returned success=false but success message:', message);
    }

    return response.data;
  },

  /**
   * Get outlet reports with optional filters
   */
  async getReports(filters: outletReportFilters = {}): Promise<outletReportsResponse> {
    const params = new URLSearchParams();

    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.endDate) params.append('endDate', filters.endDate);
    if (filters.outletName) params.append('outletName', filters.outletName);

    const response: {
      success: boolean;
      message: string;
      data: outletReportsResponse;
    } = await apiClient.get(`${BASE_URL}?${params.toString()}`);

    if (!response.success) {
      const message = response.message || 'Failed to fetch outlet reports';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      console.warn('API returned success=false but success message:', message);
    }

    return response.data;
  },

  /**
   * Get a specific outlet report by ID
   */
  async getReportById(id: number): Promise<outletReport> {
    const response: {
      success: boolean;
      message: string;
      data: outletReport;
    } = await apiClient.get(`${BASE_URL}/${id}`);

    if (!response.success) {
      const message = response.message || 'Failed to fetch outlet report';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      console.warn('API returned success=false but success message:', message);
    }

    return response.data;
  },

  /**
   * Update an existing outlet report
   */
  async updateReport(
    id: number,
    reportData: Partial<outletReportInput>
  ): Promise<outletReport> {
    const response: {
      success: boolean;
      message: string;
      data: outletReport;
    } = await apiClient.put(`${BASE_URL}/${id}`, reportData);

    if (!response.success) {
      const message = response.message || 'Failed to update outlet report';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      console.warn('API returned success=false but success message:', message);
    }

    return response.data;
  },

  /**
   * Delete a outlet report
   */
  async deleteReport(id: number): Promise<void> {
    const response: {
      success: boolean;
      message: string;
    } = await apiClient.delete(`${BASE_URL}/${id}`);

    if (!response.success) {
      const message = response.message || 'Failed to delete outlet report';
      if (!message.toLowerCase().includes('successfully')) {
        throw new Error(message);
      }
      console.warn('API returned success=false but success message:', message);
    }
  },
};
