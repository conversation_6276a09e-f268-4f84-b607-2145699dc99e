import React from 'react';
import { Building2, Clock, MapPin, Phone } from 'lucide-react';
import { outlet } from '@/features/outlets/types';

interface outletSelectorProps {
  outlets: outlet[];
  selectedoutlet: outlet | null;
  isLoading: boolean;
  error: string | null;
  onSelectoutlet: (outlet: outlet) => void;
  onProceed: () => void;
}

/**
 * outlet Selection Component
 * Allows users to select a outlet before creating a report
 */
const outletSelector: React.FC<outletSelectorProps> = ({
  outlets,
  selectedoutlet,
  isLoading,
  error,
  onSelectoutlet,
  onProceed,
}) => {
  if (isLoading) {
    return (
      <div className="mx-auto min-h-screen max-w-4xl bg-gray-50 p-6">
        <div className="rounded-lg bg-white p-8 shadow-sm">
          <div className="text-center">
            <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
            <p className="text-gray-600">Loading outlets...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mx-auto min-h-screen max-w-4xl bg-gray-50 p-6">
        <div className="rounded-lg bg-white p-8 shadow-sm">
          <div className="text-center">
            <div className="mb-4 text-red-500">
              <Building2 className="mx-auto h-12 w-12" />
            </div>
            <h2 className="mb-2 text-xl font-semibold text-gray-900">Error Loading outlets</h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto min-h-screen max-w-4xl bg-gray-50 p-6">
      <div className="rounded-lg bg-white p-8 shadow-sm">
        <div className="mb-8 text-center">
          <Building2 className="mx-auto mb-4 h-12 w-12 text-blue-600" />
          <h1 className="mb-2 text-2xl font-bold text-gray-900">Select outlet</h1>
          <p className="text-gray-600">
            Choose the outlet for which you want to create a report
          </p>
        </div>

        {outlets.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-gray-500">No active outlets found.</p>
          </div>
        ) : (
          <div className="mb-8 space-y-4">
            {outlets.map(outlet => (
              <div
                key={outlet.outletId}
                className={`cursor-pointer rounded-lg border p-4 transition-all duration-200 ${
                  selectedoutlet?.outletId === outlet.outletId
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
                onClick={() => onSelectoutlet(outlet)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="mb-2 text-lg font-semibold text-gray-900">{outlet.name}</h3>

                    <div className="grid grid-cols-1 gap-3 text-sm text-gray-600 md:grid-cols-2">
                      <div className="flex items-center">
                        <MapPin className="mr-2 h-4 w-4 text-gray-400" />
                        <span>{outlet.address}</span>
                      </div>

                      {(outlet.phoneNumber || outlet.phone) && (
                        <div className="flex items-center">
                          <Phone className="mr-2 h-4 w-4 text-gray-400" />
                          <span>{outlet.phoneNumber || outlet.phone}</span>
                        </div>
                      )}

                      {outlet.openingHours && outlet.closingHours && (
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-gray-400" />
                          <span>
                            {outlet.openingHours} - {outlet.closingHours}
                          </span>
                        </div>
                      )}
                    </div>

                    {outlet.description && (
                      <p className="mt-2 text-sm text-gray-500">{outlet.description}</p>
                    )}
                  </div>

                  <div className="ml-4">
                    <div
                      className={`h-4 w-4 rounded-full border-2 ${
                        selectedoutlet?.outletId === outlet.outletId
                          ? 'border-blue-500 bg-blue-500'
                          : 'border-gray-300'
                      }`}
                    >
                      {selectedoutlet?.outletId === outlet.outletId && (
                        <div className="h-full w-full scale-50 rounded-full bg-white"></div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedoutlet && (
          <div className="text-center">
            <button
              type="button"
              onClick={onProceed}
              className="rounded-lg bg-blue-600 px-8 py-3 font-medium text-white transition-colors duration-200 hover:bg-blue-700"
            >
              Continue with {selectedoutlet.name}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default outletSelector;
