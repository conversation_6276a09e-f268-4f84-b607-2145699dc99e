export interface FormData {
  outletName: string;
  managementNote: string;
  openTime: string;
  closeTime: string;
  totalSales: number | '';
  offlineSales: number | '';
  onlineSales: number | '';
  totalDiscounts: number | '';
  cashPayment: number | '';
  visaPayment: number | '';
  onlineTransactionPayment: number | '';
  qrPayment: number | '';
  float: number | '';
  cashIn: number | '';
  cashOut: number | '';
  expectedDrawerAmount: number | '';
  drawerAmount: number | '';
  shortExtra: number | '';
}

export interface Field {
  label: string;
  name: string;
  type: string;
  placeholder?: string;
  autoComplete?: string;
  prefix?: string;
  description?: string;
  isCalculated?: boolean;
  readOnly?: boolean;
  optional?: boolean;
}

export interface Section {
  title: string;
  icon: React.ElementType;
  description: string;
  color: string;
  fields: Field[];
}

// API Types
export interface OutletReportInput {
  outletId: number;
  managementNote: string;
  openTime: string;
  closeTime: string;
  totalSales: number;
  offlineSales: number;
  onlineSales: number;
  totalDiscounts: number;
  cashPayment: number;
  visaPayment: number;
  onlineTransactionPayment: number;
  qrPayment: number;
  float: number;
  cashIn: number;
  cashOut: number;
  expectedDrawerAmount: number;
  drawerAmount: number;
  shortExtra: number;
  reportDate?: string;
}

export interface OutletReport extends OutletReportInput {
  id: number;
  createdAt: string;
  updatedAt: string;
}

export interface OutletReportsResponse {
  data: OutletReport[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface OutletReportFilters {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
  outletName?: string;
}

// Form Validation Types
export interface ValidationErrors {
  [key: string]: string;
}

export interface FormFieldProps {
  field: Field;
  value: string | number;
  error?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
}

// Hook Return Types
export interface UseOutletReportFormReturn {
  formData: FormData;
  currentStep: number;
  completedSections: Set<number>;
  errors: ValidationErrors;
  errorSections: Set<number>;
  handleChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleNumberBlur: (e: React.FocusEvent<HTMLInputElement>) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  handleNext: () => void;
  handlePrevious: () => void;
  goToSection: (index: number) => void;
  sections: Section[];
  currentSection: Section;
  isReviewStep: boolean;
  isLastFormStep: boolean;
  canProceed: boolean;
  isSubmitting: boolean;
  submitError: string | null;
  outletName: string;
  openingHours: string;
  closingHours: string;
  isLoading: boolean;
}
