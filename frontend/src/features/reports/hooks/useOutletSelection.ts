import { useState, useEffect } from 'react';
import { outletApi } from '@/features/outlets/services/outletApi';
import { Outlet } from '@/shared/types';

export interface UseOutletSelectionReturn {
  outlets: Outlet[];
  selectedOutlet: Outlet | null;
  isLoading: boolean;
  error: string | null;
  selectOutlet: (outlet: Outlet) => void;
  clearSelection: () => void;
}

/**
 * Custom hook for managing outlet selection
 */
export const useOutletSelection = (): UseOutletSelectionReturn => {
  const [outlets, setOutlets] = useState<Outlet[]>([]);
  const [selectedOutlet, setSelectedOutlet] = useState<Outlet | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch outlets on mount
  useEffect(() => {
    const fetchOutlets = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await outletApi.getAllOutlets();
        setOutlets(data.filter(outlet => outlet.isActive === true));
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch outlets';
        setError(errorMessage);
        console.error('Error fetching outlets:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOutlets();
  }, []);

  const selectOutlet = (outlet: Outlet) => {
    setSelectedOutlet(outlet);
  };

  const clearSelection = () => {
    setSelectedOutlet(null);
  };

  return {
    outlets,
    selectedOutlet,
    isLoading,
    error,
    selectOutlet,
    clearSelection,
  };
};
