export const queryKeys = {
  // Auth
  auth: ['auth'] as const,
  currentUser: () => [...queryKeys.auth, 'current-user'] as const,

  // Users
  users: ['users'] as const,
  usersList: (filters?: any) => [...queryKeys.users, 'list', filters] as const,
  userDetail: (id: string) => [...queryKeys.users, 'detail', id] as const,

  // outlets
  outlets: ['outlets'] as const,
  outletsList: (filters?: any) => [...queryKeys.outlets, 'list', filters] as const,
  outletDetail: (id: string) => [...queryKeys.outlets, 'detail', id] as const,

  // Sales
  sales: ['sales'] as const,
  salesList: (filters?: any) => [...queryKeys.sales, 'list', filters] as const,
  salesDetail: (id: string) => [...queryKeys.sales, 'detail', id] as const,
  salesReports: (params?: any) => [...queryKeys.sales, 'reports', params] as const,

  // Dashboard
  dashboard: ['dashboard'] as const,
  adminDashboard: () => [...queryKeys.dashboard, 'admin'] as const,
  userDashboard: () => [...queryKeys.dashboard, 'user'] as const,
  analytics: (params?: any) => [...queryKeys.dashboard, 'analytics', params] as const,

  // Profile
  profile: ['profile'] as const,
  profileData: () => [...queryKeys.profile, 'data'] as const,

  // Settings
  settings: ['settings'] as const,
  settingsData: () => [...queryKeys.settings, 'data'] as const,
} as const;
