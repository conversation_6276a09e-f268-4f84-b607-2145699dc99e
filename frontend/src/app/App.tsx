import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { AuthGuard } from '@/features/auth/components/AuthGuard';
import { Layout } from '@/shared/components/layout/Layout';
import { Toaster } from '@/shared/components/ui/toaster';

// Auth Pages
import { LoginPage } from '@/features/auth/pages/LoginPage';
import { SignupPage } from '@/features/auth/pages/SignupPage';

// Dashboard Pages
import { DashboardPage } from '@/features/dashboard/pages/DashboardPage';
import { AdminDashboardPage } from '@/features/dashboard/pages/AdminDashboardPage';
import { UserDashboardPage } from '@/features/dashboard/pages/UserDashboardPage';

// Feature Pages
import { UsersPage } from '@/features/user-management/pages/UsersPage';
import { OutletsPage } from '@/features/outlets/pages/OutletsPage';
import { OutletDetailsPage } from '@/features/outlets/pages/OutletDetailsPage';
// import { SalesEntryPage } from '@/features/sales/pages/SalesEntryPage';
import SalesEntryPage from '@/features/reports/pages/SalesEntryPage';
// import { SalesReportsPage } from '@/features/sales/pages/SalesReportsPage';
import OverallRevenue from '@/features/overview/pages/OverallRevenue';
import AuditLogsPage from '@/features/audit-logs/pages/AuditLogsPage';
import { ProfilePage } from '@/features/profile/pages/ProfilePage';
import { SettingsPage } from '@/features/settings/pages/SettingsPage';
import WhatsAppLoginPage from '@/features/whatsapp/pages/WhatsAppLoginPage';

function App() {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return (
      <>
        <Toaster />
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />
          <Route path="*" element={<Navigate to="/login" replace />} />
        </Routes>
      </>
    );
  }

  return (
    <Layout>
      <Toaster />
      <Routes>
        {/* Dashboard Routes */}
        <Route path="/" element={<DashboardPage />} />
        <Route
          path="/admin"
          element={
            <AuthGuard requiredRole="admin">
              <AdminDashboardPage />
            </AuthGuard>
          }
        />
        {/* Overview  Routes */}
        <Route
          path="/overview"
          element={
            <AuthGuard requiredRole="admin">
              <OverallRevenue />
            </AuthGuard>
          }
        />
        <Route
          path="/admin/dashboard"
          element={
            <AuthGuard requiredRole="admin">
              <AdminDashboardPage />
            </AuthGuard>
          }
        />
        <Route path="/dashboard" element={<UserDashboardPage />} />

        {/* User Management Routes - Admin Only */}
        <Route
          path="/users"
          element={
            <AuthGuard requiredRole="admin">
              <UsersPage />
            </AuthGuard>
          }
        />

        {/* Outlet Management Routes - Admin Only */}
        <Route
          path="/outlets"
          element={
            <AuthGuard requiredRole="admin">
              <OutletsPage />
            </AuthGuard>
          }
        />
        <Route
          path="/outlets/:id"
          element={
            <AuthGuard requiredRole="admin">
              <OutletDetailsPage />
            </AuthGuard>
          }
        />

        {/* Sales Routes */}
        <Route path="/sales/entry" element={<SalesEntryPage />} />
        {/* <Route path="/sales/reports" element={<SalesReportsPage />} /> */}

        {/* Audit Logs Routes - Admin Only */}
        <Route
          path="/audit-logs"
          element={
            <AuthGuard requiredRole="admin">
              <AuditLogsPage />
            </AuthGuard>
          }
        />

        {/* Profile & Settings */}
        <Route path="/profile" element={<ProfilePage />} />
        <Route path="/settings" element={<SettingsPage />} />

        <Route
          path="/whatsapp/login"
          element={
            <AuthGuard requiredRole="admin">
              <WhatsAppLoginPage />
            </AuthGuard>
          }
        />
        {/* Fallback */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    </Layout>
  );
}

export default App;
