import { apiClient } from '@/lib/api/client';

// outlet interface
export interface outlet {
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// outlet API service
export const outletApi = {
  // Get all outlets
  getAll: async (): Promise<outlet[]> => {
    const response = await apiClient.get('/api/v1/outlets');
    return response.data;
  },

  // Get outlet by ID
  getById: async (id: string): Promise<outlet> => {
    const response = await apiClient.get(`/api/v1/outlets/${id}`);
    return response.data;
  },

  // Create new outlet
  create: async (data: Omit<outlet, 'id' | 'createdAt' | 'updatedAt'>): Promise<outlet> => {
    const response = await apiClient.post('/api/v1/outlets', data);
    return response.data;
  },

  // Update outlet
  update: async (
    id: string,
    data: Partial<Omit<outlet, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<outlet> => {
    const response = await apiClient.put(`/api/v1/outlets/${id}`, data);
    return response.data;
  },

  // Delete outlet
  delete: async (id: string): Promise<void> => {
    await apiClient.delete(`/api/v1/outlets/${id}`);
  },

  // Get active outlets only
  getActive: async (): Promise<outlet[]> => {
    const response = await apiClient.get('/api/v1/outlets?active=true');
    return response.data;
  },
};

export default outletApi;
