import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  href?: string;
  onClick?: () => void;
}

/**
 * Logo component with placeholder SVG that can be easily replaced
 * Features:
 * - Clickable navigation to dashboard home
 * - Multiple size variants
 * - Optional text display
 * - Customizable styling
 */
export function Logo({ className, size = 'md', showText = true, href = '/', onClick }: LogoProps) {
  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-10 w-10',
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
  };

  // Placeholder SVG logo - can be easily replaced with actual brand logo
  const LogoIcon = () => (
    <svg
      className={cn(sizeClasses[size], 'text-primary')}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      aria-label="Broku Sales Dashboard Logo"
    >
      {/* Placeholder logo design - outlet/sales themed */}
      <path
        d="M12 2L2 7L12 12L22 7L12 2Z"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="currentColor"
        fillOpacity="0.1"
      />
      <path
        d="M2 17L12 22L22 17"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 12L12 17L22 12"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      {/* Small accent dot for branding */}
      <circle cx="12" cy="7" r="1.5" fill="currentColor" />
    </svg>
  );

  const content = (
    <div
      className={cn(
        'hover:text-primary/80 flex items-center space-x-2 transition-colors',
        className
      )}
      onClick={onClick}
    >
      <LogoIcon />
      {showText && (
        <span className={cn('text-foreground font-bold', textSizeClasses[size])}>Broku</span>
      )}
    </div>
  );

  // If href is provided, wrap in Link component
  if (href) {
    return (
      <Link
        to={href}
        className="focus:ring-primary inline-flex items-center rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2"
        aria-label="Go to dashboard home"
      >
        {content}
      </Link>
    );
  }

  // Otherwise, render as a button or div
  return onClick ? (
    <button
      type="button"
      className="focus:ring-primary inline-flex items-center rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2"
      onClick={onClick}
      aria-label="Broku Sales Dashboard"
    >
      {content}
    </button>
  ) : (
    <div className="inline-flex items-center">{content}</div>
  );
}

/**
 * Logo variant for use in headers
 */
export function HeaderLogo({ className, ...props }: Omit<LogoProps, 'size'>) {
  return <Logo size="md" className={cn('text-foreground', className)} {...props} />;
}

/**
 * Logo variant for use in sidebars
 */
export function SidebarLogo({ className, ...props }: Omit<LogoProps, 'size'>) {
  return <Logo size="sm" className={cn('text-muted-foreground', className)} {...props} />;
}

/**
 * Large logo variant for landing pages or auth pages
 */
export function BrandLogo({ className, ...props }: Omit<LogoProps, 'size'>) {
  return <Logo size="lg" className={cn('text-primary', className)} {...props} />;
}
