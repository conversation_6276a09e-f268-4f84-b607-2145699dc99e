import React from 'react';
import { useTheme } from 'next-themes';
import { Toaster as Sonner } from 'sonner';

type ToasterProps = React.ComponentProps<typeof Sonner>;

/**
 * Centralized Toaster component with theme support and consistent styling
 * Uses Sonner for modern, accessible toast notifications
 */
const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme();

  return (
    <Sonner
      theme={theme as ToasterProps['theme']}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',
          description: 'group-[.toast]:text-muted-foreground',
          actionButton: 'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',
          cancelButton: 'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground',
          closeButton: 'group-[.toast]:bg-background group-[.toast]:text-foreground',
          error: 'group-[.toast]:bg-destructive group-[.toast]:text-destructive-foreground',
          success: 'group-[.toast]:bg-green-600 group-[.toast]:text-white',
          warning: 'group-[.toast]:bg-yellow-600 group-[.toast]:text-white',
          info: 'group-[.toast]:bg-blue-600 group-[.toast]:text-white',
        },
      }}
      position="bottom-right"
      expand={true}
      richColors={true}
      closeButton={false}
      {...props}
    />
  );
};

export { Toaster };
