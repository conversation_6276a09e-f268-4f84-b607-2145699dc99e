import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

export interface BreadcrumbItem {
  label: string;
  href?: string;
  isCurrentPage?: boolean;
}

// Route to label mapping
const ROUTE_LABELS: Record<string, string> = {
  '/': 'Dashboard',
  '/dashboard': 'Dashboard',
  '/admin': 'Admin Dashboard',
  '/admin/dashboard': 'Admin Dashboard',
  '/overview': 'Overview',
  '/users': 'User Management',
  '/outlets': 'outlet Management',
  '/sales': 'Sales',
  '/sales/entry': 'Sales Entry',
  '/sales/reports': 'Sales Reports',
  '/audit-logs': 'Audit Logs',
  '/profile': 'Profile',
  '/settings': 'Settings',
  '/reports': 'Reports',
  '/analysis': 'Analysis',
};

// Dynamic route patterns (for routes with parameters)
const DYNAMIC_ROUTE_PATTERNS: Array<{
  pattern: RegExp;
  getLabel: (matches: RegExpMatchArray) => string;
}> = [
  {
    pattern: /^\/outlets\/(\d+)$/,
    getLabel: () => 'outlet Details',
  },
  {
    pattern: /^\/users\/(\d+)$/,
    getLabel: () => 'User Details',
  },
  {
    pattern: /^\/sales\/(\d+)$/,
    getLabel: () => 'Sale Details',
  },
];

/**
 * Custom hook for generating breadcrumb navigation based on current route
 */
export function useBreadcrumbs(): BreadcrumbItem[] {
  const location = useLocation();

  return useMemo(() => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always start with Dashboard as home
    if (location.pathname !== '/') {
      breadcrumbs.push({
        label: 'Dashboard',
        href: '/',
        isCurrentPage: false,
      });
    }

    // Build breadcrumbs from path segments
    let currentPath = '';

    for (let i = 0; i < pathSegments.length; i++) {
      currentPath += `/${pathSegments[i]}`;
      const isLastSegment = i === pathSegments.length - 1;

      // Check if this is a known static route
      let label = ROUTE_LABELS[currentPath];

      // If not found, check dynamic patterns
      if (!label) {
        for (const { pattern, getLabel } of DYNAMIC_ROUTE_PATTERNS) {
          const matches = currentPath.match(pattern);
          if (matches) {
            label = getLabel(matches);
            break;
          }
        }
      }

      // Fallback to capitalized segment name
      if (!label) {
        label = pathSegments[i]
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      }

      // Add breadcrumb item
      breadcrumbs.push({
        label,
        href: isLastSegment ? undefined : currentPath,
        isCurrentPage: isLastSegment,
      });
    }

    // Handle root path
    if (location.pathname === '/') {
      breadcrumbs.push({
        label: 'Dashboard',
        isCurrentPage: true,
      });
    }

    return breadcrumbs;
  }, [location.pathname]);
}

/**
 * Hook variant that excludes the current page from breadcrumbs
 * Useful when you want to show only the navigation path
 */
export function useBreadcrumbsNavigation(): BreadcrumbItem[] {
  const breadcrumbs = useBreadcrumbs();
  return breadcrumbs.filter(item => !item.isCurrentPage);
}

/**
 * Hook that returns breadcrumb data with additional metadata
 */
export function useBreadcrumbsWithMetadata() {
  const breadcrumbs = useBreadcrumbs();
  const location = useLocation();

  return useMemo(
    () => ({
      breadcrumbs,
      currentPage: breadcrumbs.find(item => item.isCurrentPage)?.label || 'Unknown',
      hasNavigation: breadcrumbs.length > 1,
      isHomePage: location.pathname === '/',
      pathDepth: breadcrumbs.length,
    }),
    [breadcrumbs, location.pathname]
  );
}
