// Types for header component enhancements

/**
 * Breadcrumb navigation item interface
 */
export interface BreadcrumbItem {
  /** Display label for the breadcrumb */
  label: string;
  /** Optional href for navigation (undefined for current page) */
  href?: string;
  /** Whether this is the current page */
  isCurrentPage?: boolean;
  /** Optional icon component */
  icon?: React.ComponentType<{ className?: string }>;
}

/**
 * Breadcrumb navigation metadata
 */
export interface BreadcrumbMetadata {
  /** Array of breadcrumb items */
  breadcrumbs: BreadcrumbItem[];
  /** Current page label */
  currentPage: string;
  /** Whether navigation breadcrumbs exist */
  hasNavigation: boolean;
  /** Whether user is on home page */
  isHomePage: boolean;
  /** Depth of current path */
  pathDepth: number;
}

/**
 * Header outlet display data
 */
export interface HeaderoutletData {
  /** outlet ID */
  id: string;
  /** outlet name for display */
  name: string;
  /** Whether outlet is active */
  isActive: boolean;
  /** Optional outlet address */
  address?: string;
  /** Loading state */
  isLoading?: boolean;
  /** Error state */
  error?: string | null;
}

/**
 * Header user display data
 */
export interface HeaderUserData {
  /** User ID */
  userId: number;
  /** Username */
  username: string;
  /** User's email address */
  email: string;
  /** User's full name */
  fullName: string;
  /** User role */
  role: 'admin' | 'staff' | 'user';
  /** Associated outlet ID */
  outletId: number | null;
}

/**
 * Enhanced header component props
 */
export interface EnhancedHeaderProps {
  /** Optional custom logo component */
  logoComponent?: React.ComponentType<any>;
  /** Whether to show breadcrumbs */
  showBreadcrumbs?: boolean;
  /** Whether to show outlet name */
  showoutletName?: boolean;
  /** Custom breadcrumb items (overrides auto-generated) */
  customBreadcrumbs?: BreadcrumbItem[];
  /** Additional CSS classes */
  className?: string;
  /** Whether to hide breadcrumbs on mobile */
  hideBreadcrumbsOnMobile?: boolean;
}

/**
 * Header state interface for managing loading and error states
 */
export interface HeaderState {
  /** outlet data loading state */
  outletLoading: boolean;
  /** outlet data error */
  outletError: string | null;
  /** User data loading state */
  userLoading: boolean;
  /** User data error */
  userError: string | null;
  /** Breadcrumbs loading state */
  breadcrumbsLoading: boolean;
}

/**
 * Header actions interface
 */
export interface HeaderActions {
  /** Handle logo click */
  onLogoClick?: () => void;
  /** Handle breadcrumb item click */
  onBreadcrumbClick?: (item: BreadcrumbItem) => void;
  /** Handle outlet name click */
  onoutletClick?: (outlet: HeaderoutletData) => void;
  /** Handle user dropdown actions */
  onUserAction?: (action: 'profile' | 'settings' | 'logout') => void;
}

/**
 * Logo component props interface
 */
export interface LogoProps {
  /** Additional CSS classes */
  className?: string;
  /** Logo size variant */
  size?: 'sm' | 'md' | 'lg';
  /** Whether to show text alongside logo */
  showText?: boolean;
  /** Navigation href */
  href?: string;
  /** Click handler */
  onClick?: () => void;
}

/**
 * outlet name display component props
 */
export interface outletNameProps {
  /** outlet data */
  outlet: HeaderoutletData | null;
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Click handler */
  onClick?: (outlet: HeaderoutletData) => void;
  /** Additional CSS classes */
  className?: string;
}

/**
 * Enhanced user dropdown props
 */
export interface EnhancedUserDropdownProps {
  /** User data */
  user: HeaderUserData | null;
  /** Logout handler */
  onLogout: () => void;
  /** Navigation handler */
  onNavigate: (path: string) => void;
  /** Additional CSS classes */
  className?: string;
}
