{"version": "5", "dialect": "mysql", "id": "c7781c1a-b162-4ba3-9b93-b582ee0305d5", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"audit_logs": {"name": "audit_logs", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "resource_type": {"name": "resource_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "resource_id": {"name": "resource_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "old_values": {"name": "old_values", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "new_values": {"name": "new_values", "type": "json", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_id_idx": {"name": "user_id_idx", "columns": ["user_id"], "isUnique": false}, "action_idx": {"name": "action_idx", "columns": ["action"], "isUnique": false}, "resource_type_idx": {"name": "resource_type_idx", "columns": ["resource_type"], "isUnique": false}, "resource_id_idx": {"name": "resource_id_idx", "columns": ["resource_id"], "isUnique": false}, "created_at_idx": {"name": "created_at_idx", "columns": ["created_at"], "isUnique": false}, "deleted_at_idx": {"name": "deleted_at_idx", "columns": ["deleted_at"], "isUnique": false}, "user_action_idx": {"name": "user_action_idx", "columns": ["user_id", "action"], "isUnique": false}, "resource_idx": {"name": "resource_idx", "columns": ["resource_type", "resource_id"], "isUnique": false}}, "foreignKeys": {"audit_logs_user_id_users_user_id_fk": {"name": "audit_logs_user_id_users_user_id_fk", "tableFrom": "audit_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"audit_logs_id": {"name": "audit_logs_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "outlets": {"name": "outlets", "columns": {"outlet_id": {"name": "outlet_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "address": {"name": "address", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}, "opening_time": {"name": "opening_time", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "closing_time": {"name": "closing_time", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"name_idx": {"name": "name_idx", "columns": ["name"], "isUnique": false}, "is_active_idx": {"name": "is_active_idx", "columns": ["is_active"], "isUnique": false}, "deleted_at_idx": {"name": "deleted_at_idx", "columns": ["deleted_at"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"outlets_outlet_id": {"name": "outlets_outlet_id", "columns": ["outlet_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "sales": {"name": "sales", "columns": {"sales_id": {"name": "sales_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "outlet_id": {"name": "outlet_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "sales_date": {"name": "sales_date", "type": "date", "primaryKey": false, "notNull": true, "autoincrement": false}, "cash_amount": {"name": "cash_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "card_amount": {"name": "card_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "online_payment_amount": {"name": "online_payment_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "total_sales": {"name": "total_sales", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "total_discounts": {"name": "total_discounts", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "recorded_by_user_id": {"name": "recorded_by_user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "recorded_at": {"name": "recorded_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"outlet_id_idx": {"name": "outlet_id_idx", "columns": ["outlet_id"], "isUnique": false}, "sales_date_idx": {"name": "sales_date_idx", "columns": ["sales_date"], "isUnique": false}, "recorded_by_user_id_idx": {"name": "recorded_by_user_id_idx", "columns": ["recorded_by_user_id"], "isUnique": false}, "deleted_at_idx": {"name": "deleted_at_idx", "columns": ["deleted_at"], "isUnique": false}, "outlet_sales_date_idx": {"name": "outlet_sales_date_idx", "columns": ["outlet_id", "sales_date"], "isUnique": false}}, "foreignKeys": {"sales_outlet_id_outlets_outlet_id_fk": {"name": "sales_outlet_id_outlets_outlet_id_fk", "tableFrom": "sales", "tableTo": "outlets", "columnsFrom": ["outlet_id"], "columnsTo": ["outlet_id"], "onDelete": "no action", "onUpdate": "no action"}, "sales_recorded_by_user_id_users_user_id_fk": {"name": "sales_recorded_by_user_id_users_user_id_fk", "tableFrom": "sales", "tableTo": "users", "columnsFrom": ["recorded_by_user_id"], "columnsTo": ["user_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"sales_sales_id": {"name": "sales_sales_id", "columns": ["sales_id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "users": {"name": "users", "columns": {"user_id": {"name": "user_id", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": true}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "enum('admin','staff','user')", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'user'"}, "full_name": {"name": "full_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "outlet_id": {"name": "outlet_id", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "autoincrement": false, "default": true}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "autoincrement": false, "onUpdate": true, "default": "(now())"}}, "indexes": {"outlet_id_idx": {"name": "outlet_id_idx", "columns": ["outlet_id"], "isUnique": false}}, "foreignKeys": {"users_outlet_id_outlets_outlet_id_fk": {"name": "users_outlet_id_outlets_outlet_id_fk", "tableFrom": "users", "tableTo": "outlets", "columnsFrom": ["outlet_id"], "columnsTo": ["outlet_id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"users_user_id": {"name": "users_user_id", "columns": ["user_id"]}}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "columns": ["email"]}}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}