CREATE TABLE `audit_logs` (
	`id` varchar(36) NOT NULL,
	`user_id` int,
	`action` varchar(100) NOT NULL,
	`resource_type` varchar(50),
	`resource_id` varchar(100),
	`old_values` json,
	`new_values` json,
	`ip_address` varchar(45),
	`user_agent` text,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`deleted_at` timestamp,
	CONSTRAINT `audit_logs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `outlets` (
	`outlet_id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	`address` varchar(500) NOT NULL,
	`phone_number` varchar(20) NOT NULL,
	`opening_time` varchar(10) NOT NULL,
	`closing_time` varchar(10) NOT NULL,
	`is_active` boolean NOT NULL DEFAULT true,
	`description` text,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`updated_at` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` timestamp,
	CONSTRAINT `outlets_outlet_id` PRIMARY KEY(`outlet_id`)
);
--> statement-breakpoint
CREATE TABLE `sales` (
	`sales_id` int AUTO_INCREMENT NOT NULL,
	`outlet_id` int NOT NULL,
	`sales_date` date NOT NULL,
	`cash_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
	`card_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
	`online_payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
	`total_sales` decimal(10,2) NOT NULL,
	`total_discounts` decimal(10,2) NOT NULL DEFAULT '0.00',
	`recorded_by_user_id` int NOT NULL,
	`notes` text,
	`recorded_at` timestamp NOT NULL DEFAULT (now()),
	`updated_at` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	`deleted_at` timestamp,
	CONSTRAINT `sales_sales_id` PRIMARY KEY(`sales_id`)
);
--> statement-breakpoint
CREATE TABLE `users` (
	`user_id` int AUTO_INCREMENT NOT NULL,
	`username` varchar(100) NOT NULL,
	`email` varchar(255) NOT NULL,
	`password` varchar(255) NOT NULL,
	`role` enum('admin','staff','user') NOT NULL DEFAULT 'user',
	`full_name` varchar(255) NOT NULL,
	`outlet_id` int,
	`is_active` boolean NOT NULL DEFAULT true,
	`last_login_at` timestamp,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`updated_at` timestamp NOT NULL DEFAULT (now()) ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `users_user_id` PRIMARY KEY(`user_id`),
	CONSTRAINT `users_username_unique` UNIQUE(`username`),
	CONSTRAINT `users_email_unique` UNIQUE(`email`)
);
--> statement-breakpoint
ALTER TABLE `audit_logs` ADD CONSTRAINT `audit_logs_user_id_users_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`user_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `sales` ADD CONSTRAINT `sales_outlet_id_outlets_outlet_id_fk` FOREIGN KEY (`outlet_id`) REFERENCES `outlets`(`outlet_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `sales` ADD CONSTRAINT `sales_recorded_by_user_id_users_user_id_fk` FOREIGN KEY (`recorded_by_user_id`) REFERENCES `users`(`user_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `users` ADD CONSTRAINT `users_outlet_id_outlets_outlet_id_fk` FOREIGN KEY (`outlet_id`) REFERENCES `outlets`(`outlet_id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX `user_id_idx` ON `audit_logs` (`user_id`);--> statement-breakpoint
CREATE INDEX `action_idx` ON `audit_logs` (`action`);--> statement-breakpoint
CREATE INDEX `resource_type_idx` ON `audit_logs` (`resource_type`);--> statement-breakpoint
CREATE INDEX `resource_id_idx` ON `audit_logs` (`resource_id`);--> statement-breakpoint
CREATE INDEX `created_at_idx` ON `audit_logs` (`created_at`);--> statement-breakpoint
CREATE INDEX `deleted_at_idx` ON `audit_logs` (`deleted_at`);--> statement-breakpoint
CREATE INDEX `user_action_idx` ON `audit_logs` (`user_id`,`action`);--> statement-breakpoint
CREATE INDEX `resource_idx` ON `audit_logs` (`resource_type`,`resource_id`);--> statement-breakpoint
CREATE INDEX `name_idx` ON `outlets` (`name`);--> statement-breakpoint
CREATE INDEX `is_active_idx` ON `outlets` (`is_active`);--> statement-breakpoint
CREATE INDEX `deleted_at_idx` ON `outlets` (`deleted_at`);--> statement-breakpoint
CREATE INDEX `outlet_id_idx` ON `sales` (`outlet_id`);--> statement-breakpoint
CREATE INDEX `sales_date_idx` ON `sales` (`sales_date`);--> statement-breakpoint
CREATE INDEX `recorded_by_user_id_idx` ON `sales` (`recorded_by_user_id`);--> statement-breakpoint
CREATE INDEX `deleted_at_idx` ON `sales` (`deleted_at`);--> statement-breakpoint
CREATE INDEX `outlet_sales_date_idx` ON `sales` (`outlet_id`,`sales_date`);--> statement-breakpoint
CREATE INDEX `outlet_id_idx` ON `users` (`outlet_id`);