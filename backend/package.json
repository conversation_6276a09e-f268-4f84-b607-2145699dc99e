{"name": "@broku/backend", "version": "1.0.0", "description": "A TypeScript + Express backend with clean architecture, featuring authentication, validation, logging, and database integration.", "main": "dist/src/client/api.js", "scripts": {"dev": "tsx watch src/client/api.ts", "build": "tsc", "start": "node dist/src/client/api.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate --config=./drizzle.config.ts", "db:push": "drizzle-kit push --config=./drizzle.config.ts --force", "db:pull": "drizzle-kit pull", "db:studio": "drizzle-kit studio", "db:seed": "tsx src/infrastructure/db/seed.ts"}, "keywords": ["typescript", "express", "zod", "pino", "drizzle", "mysql", "backend"], "author": "", "license": "ISC", "dependencies": {"@broku/shared-types": "workspace:*", "@broku/shared-utils": "workspace:*", "@broku/shared-validations": "workspace:*", "@hapi/boom": "^10.0.1", "@types/bcryptjs": "^3.0.0", "@whiskeysockets/baileys": "^6.7.5", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^17.0.0", "drizzle-orm": "^0.44.2", "drizzle-seed": "^0.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "node-cache": "^5.1.2", "node-schedule": "^2.1.1", "pino": "^8.16.2", "pino-http": "^8.5.1", "pino-pretty": "^13.0.0", "qr-image": "^3.2.0", "qrcode-terminal": "^0.12.0", "zod": "^3.25.67"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.9.0", "@types/node-schedule": "^2.1.7", "@types/qr-image": "^3.2.9", "drizzle-kit": "^0.31.4", "tsx": "^4.6.0"}}