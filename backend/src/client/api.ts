import express from 'express';
import cors from 'cors';
import pinoHttp from 'pino-http';
import { config } from '../infrastructure/config';
import { logger } from '../infrastructure/logger/pino';
import { connectToDatabase, disconnectFromDatabase } from '../infrastructure/db/client';
import router from '../presentation';
import { scheduleDailySalesSummary } from '../business/services/whatsapp/scheduler';

// Create Express application
const app: express.Application = express();

// Configure CORS
app.use(
  cors({
    origin: config.nodeEnv === 'development' ? '*' : process.env['ALLOWED_ORIGINS']?.split(','),
    credentials: true,
  })
);

// Request logging middleware
app.use(
  pinoHttp({
    logger,
    customLogLevel: (_req, res, err) => {
      if (res.statusCode >= 400 && res.statusCode < 500) {
        return 'warn';
      } else if (res.statusCode >= 500 || err) {
        return 'error';
      }
      return 'info';
    },
    customSuccessMessage: (req, res) => {
      return `${req.method} ${req.url} - ${res.statusCode}`;
    },
    customErrorMessage: (req, res, err) => {
      return `${req.method} ${req.url} - ${res.statusCode} - ${err.message}`;
    },
  })
);

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// API routes
app.use('/api/v1', router);

// Global error handler
app.use((err: Error, _req: express.Request, res: express.Response, _next: express.NextFunction) => {
  logger.error('Unhandled error:', err);

  res.status(500).json({
    success: false,
    message: config.nodeEnv === 'development' ? err.message : 'Internal server error',
    ...(config.nodeEnv === 'development' && { stack: err.stack }),
  });
});

// Start server function
const startServer = async (): Promise<void> => {
  try {
    console.log('🚀 Starting server...');
    // Connect to database
    await connectToDatabase();

    // Start HTTP server
    console.log(`🚀 Starting HTTP server on port ${config.port}...`);
    const server = app.listen(config.port, () => {
      console.log(`✅ Server running on port ${config.port}`);
      console.log(`📝 Environment: ${config.nodeEnv}`);
      console.log(`🔗 API Base URL: http://localhost:${config.port}`);
      logger.info(`🚀 Server running on port ${config.port}`);
      logger.info(`📝 Environment: ${config.nodeEnv}`);
      logger.info(`🔗 API Base URL: http://localhost:${config.port}`);

      // Initialize WhatsApp connection
      // if (config.whatsapp.enabled) {
      // The WhatsApp service is initialized automatically when the module is imported.
      // No explicit start call is needed here.
      // scheduleDailySalesSummary();
      // }
    });

    // Graceful shutdown
    const gracefulShutdown = (signal: string): void => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);

      server.close(() => {
        logger.info('HTTP server closed');

        disconnectFromDatabase()
          .then(() => {
            logger.info('✅ Graceful shutdown completed');
            process.exit(0);
          })
          .catch(error => {
            logger.error('❌ Error during shutdown:', error);
            process.exit(1);
          });
      });
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
if (require.main === module) {
  startServer().catch(error => {
    logger.error('❌ Server startup failed:', error);
    process.exit(1);
  });
}

export { app };
