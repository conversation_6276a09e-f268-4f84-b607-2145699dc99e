import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
import bcrypt from 'bcryptjs';
import { config } from '../config';
import { users, sales, outlets } from '../../data/models';

// Create database connection for seeding
const connection = mysql.createPool({
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: config.database.name,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

const db = drizzle(connection);

async function seedAdmin() {
  console.log('🌱 Starting admin user seeding...');

  try {
    // Clear existing data (in reverse order due to foreign keys)
    console.log('🧹 Clearing existing data...');
    await db.delete(sales);
    await db.delete(users);
    await db.delete(outlets);

    // Seed admin user
    console.log('👤 Seeding admin user...');
    const hashedPassword = await bcrypt.hash('password123', 12);

    const adminData = {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'admin' as const,
      fullName: 'System Administrator',
      outletId: null, // Admin can access all outlets
    };

    await db.insert(users).values(adminData);
    console.log('✅ Inserted admin user');

    console.log('🎉 Admin user seeding completed successfully!');
    console.log('\n🔐 Default login credentials:');
    console.log('- Admin: admin / password123');

  } catch (error) {
    console.error('❌ Error seeding admin user:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedAdmin()
    .then(() => {
      console.log('✅ Seeding process completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding process failed:', error);
      process.exit(1);
    });
}

export { seedAdmin };