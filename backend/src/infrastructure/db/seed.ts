import { drizzle } from 'drizzle-orm/mysql2';
import mysql from 'mysql2/promise';
// import { seed } from 'drizzle-seed';
import bcrypt from 'bcryptjs';
import { config } from '../config';
import { outlets, users, sales } from '../../data/models';

// Create database connection for seeding
const connection = mysql.createPool({
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: config.database.name,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

const db = drizzle(connection);

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');

  try {
    // Clear existing data (in reverse order due to foreign keys)
    console.log('🧹 Clearing existing data...');
    await db.delete(sales);
    await db.delete(users);
    await db.delete(outlets);

    // Seed outlets
    console.log('🏪 Seeding outlets...');
    const outletData = [
      {
        name: 'Broku Downtown',
        address: '123 Main Street, Downtown, NY 10001',
        phoneNumber: '******-0101',
        openingTime: '08:00',
        closingTime: '22:00',
        isActive: true,
        description: 'Our flagship location in the heart of downtown, serving authentic cuisine since 2020.',
      },
      {
        name: 'Broku Uptown',
        address: '456 Broadway Avenue, Uptown, NY 10002',
        phoneNumber: '******-0102',
        openingTime: '09:00',
        closingTime: '23:00',
        isActive: true,
        description: 'Modern dining experience with a focus on fresh, locally sourced ingredients.',
      },
      {
        name: 'Broku Express',
        address: '789 Quick Street, Midtown, NY 10003',
        phoneNumber: '******-0103',
        openingTime: '07:00',
        closingTime: '20:00',
        isActive: true,
        description: 'Fast-casual dining for busy professionals and students.',
      },
      {
        name: 'Broku Seaside',
        address: '321 Ocean Drive, Coastal, NY 10004',
        phoneNumber: '******-0104',
        openingTime: '10:00',
        closingTime: '21:00',
        isActive: false,
        description: 'Temporarily closed for renovations. Reopening Spring 2025.',
      },
    ];

    await db.insert(outlets).values(outletData);
    console.log(`✅ Inserted ${outletData.length} outlets`);

    // Get outlet IDs for foreign key references
    const allOutlets = await db.select().from(outlets);
    const outletIds = allOutlets.map((r) => r.outletId);

    // Seed users
    console.log('👥 Seeding users...');
    const hashedPassword = await bcrypt.hash('password123', 12);

    const userData = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin' as const,
        fullName: 'System Administrator',
        outletId: null, // Admin can access all outlets
      },
      {
        username: 'manager_downtown',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'staff' as const,
        fullName: 'John Smith',
        outletId: outletIds[0],
      },
      {
        username: 'manager_uptown',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'staff' as const,
        fullName: 'Sarah Johnson',
        outletId: outletIds[1],
      },
      {
        username: 'cashier_downtown',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'user' as const,
        fullName: 'Mike Davis',
        outletId: outletIds[0],
      },
      {
        username: 'cashier_uptown',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'user' as const,
        fullName: 'Emily Wilson',
        outletId: outletIds[1],
      },
      {
        username: 'staff_express',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'user' as const,
        fullName: 'David Brown',
        outletId: outletIds[2],
      },
    ];

    await db.insert(users).values(userData);
    console.log(`✅ Inserted ${userData.length} users`);

    // Get user IDs for foreign key references
    const allUsers = await db.select().from(users);
    // const userIds = allUsers.map((u) => u.userId);

    console.log('💰 Seeding sales data...');
    // Generate sales data for the last 15 months to support 12-month chart view
    const salesData = [];
    const today = new Date();

    for (let i = 0; i < 450; i++) { // ~15 months of data (30 days * 15 months)
      const salesDate = new Date(today);
      salesDate.setDate(today.getDate() - i);

      // Generate 1-3 sales records per day for each active outlet
      const activeOutlets = allOutlets.filter(r => r.isActive);
      for (const outlet of activeOutlets) {
        const numSales = Math.floor(Math.random() * 3) + 1;

        for (let saleIndex = 0; saleIndex < numSales; saleIndex++) {
          const cashAmount = Math.floor(Math.random() * 500) + 100;
          const cardAmount = Math.floor(Math.random() * 800) + 200;
          const onlineAmount = Math.floor(Math.random() * 300) + 50;
          const totalSales = cashAmount + cardAmount + onlineAmount;
          const discounts = Math.floor(Math.random() * 50);

          // Assign to appropriate staff member
          const staffForOutlet = allUsers.filter(u => u.outletId === outlet.outletId);
          let recordedByUserId: number;
          if (staffForOutlet.length > 0) {
            recordedByUserId = staffForOutlet[Math.floor(Math.random() * staffForOutlet.length)]!.userId;
          } else if (allUsers.length > 0) {
            recordedByUserId = allUsers[0]!.userId; // Fallback to admin user
          } else {
            // This case should not happen in this seed script, but it's good practice to handle it.
            throw new Error('No users found to assign to sales record.');
          }

          salesData.push({
            outletId: outlet.outletId,
            salesDate,
            cashAmount: cashAmount.toFixed(2),
            cardAmount: cardAmount.toFixed(2),
            onlinePaymentAmount: onlineAmount.toFixed(2),
            totalSales: totalSales.toFixed(2),
            totalDiscounts: discounts.toFixed(2),
            recordedByUserId,
            notes: saleIndex === 0 ? `Daily sales report for ${salesDate.toDateString()}` : null,
          });
        }
      }
    }

    // Insert sales data in batches
    const batchSize = 50;
    for (let i = 0; i < salesData.length; i += batchSize) {
      const batch = salesData.slice(i, i + batchSize);
      await db.insert(sales).values(batch);
    }

    console.log(`✅ Inserted ${salesData.length} sales records`);

    console.log('🎉 Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`- ${outletData.length} outlets`);
    console.log(`- ${userData.length} users`);
    console.log(`- ${salesData.length} sales records`);
    console.log('\n🔐 Default login credentials:');
    console.log('- Admin: admin / password123');
    console.log('- Manager Downtown: manager_downtown / password123');
    console.log('- Manager Uptown: manager_uptown / password123');
    console.log('- Cashier Downtown: cashier_downtown / password123');
    console.log('- Cashier Uptown: cashier_uptown / password123');
    console.log('- Staff Express: staff_express / password123');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    await connection.end();
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ Seeding process completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding process failed:', error);
      process.exit(1);
    });
}

export { seedDatabase };
