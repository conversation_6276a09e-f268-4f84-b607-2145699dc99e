// Repository interface abstraction for database-agnostic user operations
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UserQueryParams {
  role?: string;
  outletId?: number;
  search?: string;
  sortBy?: 'username' | 'fullName' | 'role';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface CreateUserData {
  username: string;
  email: string;
  password: string;
  fullName: string;
  role: string;
  outletId?: number;
}

export interface UpdateUserData {
  username?: string;
  email?: string;
  password?: string;
  fullName?: string;
  role?: string;
  outletId?: number;
  isActive?: boolean;
  lastLoginAt?: Date;
}

export interface GlobalUserStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  newUsersThisMonth: number;
  usersByRole: Record<string, number>;
}

export interface IndividualUserStats {
  userId: number;
  createdAt: Date;
  lastLoginAt: Date | null;
  sessionCount: number;
  avgSessionDuration: number; // in minutes
}

// Database-agnostic user repository interface
export interface IUserRepository {
  // Query operations
  findAll(query: UserQueryParams): Promise<PaginatedResult<User>>;
  findById(id: number): Promise<User | null>;
  findSafeById(id: number): Promise<SafeUser | null>;
  findByUsername(username: string): Promise<User | null>;
  findByEmail(email: string): Promise<User | null>;
  findByOutletId(outletId: number): Promise<User[]>;
  getGlobalStats(): Promise<GlobalUserStats>;
  getIndividualStats(userId: number): Promise<IndividualUserStats | null>;

  // Command operations
  create(userData: CreateUserData): Promise<SafeUser>;
  update(id: number, data: Partial<UpdateUserData>): Promise<SafeUser | null>;
  delete(id: number): Promise<boolean>;

  // Utility operations
  usernameExists(username: string, excludeId?: number): Promise<boolean>;
  emailExists(email: string, excludeId?: number): Promise<boolean>;
  toSafeUser(user: User): SafeUser;
}

// Domain models (separate from database entities)
export interface User {
  userId: number;
  username: string;
  email: string;
  password: string;
  fullName: string;
  role: string;
  outletId: number | null;
  isActive: boolean;
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface SafeUser {
  userId: number;
  username: string;
  email: string;
  fullName: string;
  role: 'admin' | 'staff' | 'user';
  outletId: number | null;
  isActive: boolean;
  lastLoginAt: Date | null;
  createdAt: Date;
  updatedAt: Date;
}
