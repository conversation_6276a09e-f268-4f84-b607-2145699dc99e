// Repository interface abstraction for database-agnostic sales operations
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface SalesQueryParams {
  outletId?: number | null;
  recordedBy?: number;
  startDate?: string;
  endDate?: string;
  sortBy?: 'salesDate' | 'totalSales' | 'recordedAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface CreateSalesData {
  outletId: number;
  salesDate: Date;
  totalSales: string;
  cashAmount: string;
  cardAmount: string;
  onlinePaymentAmount: string;
  totalDiscounts: string;
  notes?: string;
  recordedByUserId: number;
}

export interface UpdateSalesData {
  salesDate?: Date;
  totalSales?: string;
  cashAmount?: string;
  cardAmount?: string;
  onlinePaymentAmount?: string;
  totalDiscounts?: string;
  notes?: string;
}

// Database-agnostic sales repository interface
export interface ISalesRepository {
  // Query operations
  findAll(query: SalesQueryParams): Promise<PaginatedResult<Sales>>;
  findById(id: number): Promise<Sales | null>;
  findDuplicateEntry(outletId: number, salesDate: string): Promise<Sales | null>;
  findByDateRange(startDate: string, endDate: string, outletId?: number): Promise<Sales[]>;
  getSalesSummary(startDate: string, endDate: string, outletId?: number): Promise<SalesSummary>;
  
  // Command operations
  create(salesData: CreateSalesData): Promise<Sales>;
  update(id: number, data: Partial<UpdateSalesData>): Promise<Sales | null>;
  softDelete(id: number): Promise<boolean>;
  restore(id: number): Promise<boolean>;
}

// Domain models (separate from database entities)
export interface Sales {
  salesId: number;
  outletId: number;
  salesDate: string;
  totalSales: string;
  cashAmount: string;
  cardAmount: string;
  onlinePaymentAmount: string;
  paymentMethod: 'cash' | 'card' | 'online' | 'multiple' | null;
  totalDiscounts: string;
  notes: string | null;
  recordedByUserId: number;
  recordedAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

export interface SalesSummary {
  totalSales: string;
  totalCash: string;
  totalCard: string;
  totalOnline: string;
  totalDiscounts: string;
  recordCount: number;
  averageSales: string;
}
