import { z } from 'zod';

// Base audit log schema
export const auditLogSchema = z.object({
  id: z.string().uuid(),
  userId: z.number().int().positive().nullable(),
  action: z.string().min(1).max(100),
  resourceType: z.string().min(1).max(50).optional(),
  resourceId: z.string().min(1).max(100).optional(),
  oldValues: z.record(z.any()).optional(),
  newValues: z.record(z.any()).optional(),
  ipAddress: z.string().ip().optional(),
  userAgent: z.string().max(1000).optional(),
  createdAt: z.date(),
  deletedAt: z.date().nullable(),
});

// Create audit log schema
export const createAuditLogSchema = z.object({
  userId: z.number().int().positive().nullable().optional(),
  action: z.string().min(1).max(100),
  resourceType: z.string().min(1).max(50).optional(),
  resourceId: z.string().min(1).max(100).optional(),
  oldValues: z.record(z.any()).optional(),
  newValues: z.record(z.any()).optional(),
  ipAddress: z.string().ip().optional(),
  userAgent: z.string().max(1000).optional(),
});

// Bulk create audit logs schema
export const bulkCreateAuditLogsSchema = z.object({
  auditLogs: z.array(createAuditLogSchema).min(1).max(1000),
});

// Audit log query parameters schema
export const auditLogQuerySchema = z.object({
  search: z.string().optional(),
  action: z.string().optional(),
  userId: z.coerce.number().int().positive().optional(),
  performedBy: z.coerce.number().int().positive().optional(),
  resourceType: z.string().optional(),
  resourceId: z.string().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(1000).default(50),
  sortBy: z.enum(['createdAt', 'action', 'userId']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  includeDeleted: z.coerce.boolean().default(false),
});

// Audit log ID parameter schema
export const auditLogIdParamSchema = z.object({
  id: z.string().uuid(),
});

// User ID parameter schema for user audit logs
export const userIdParamSchema = z.object({
  userId: z.coerce.number().int().positive(),
});

// Resource audit logs parameters schema
export const resourceAuditLogsParamSchema = z.object({
  resourceType: z.string().min(1).max(50),
  resourceId: z.string().min(1).max(100),
});

// Audit log statistics query schema
export const auditLogStatsQuerySchema = z.object({
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  userId: z.coerce.number().int().positive().optional(),
  resourceType: z.string().optional(),
});

// Cleanup old logs schema
export const cleanupOldLogsSchema = z.object({
  olderThanDays: z.coerce.number().int().min(30).max(3650), // 30 days to 10 years
});

// Restore audit log schema
export const restoreAuditLogSchema = z.object({
  id: z.string().uuid(),
});

// Export audit log schema
export const exportAuditLogsSchema = z.object({
  format: z.enum(['csv', 'json', 'xlsx']).default('csv'),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  filters: auditLogQuerySchema.omit({ page: true, limit: true }).optional(),
});

// Predefined action values for validation
export const auditActionSchema = z.enum([
  // User actions
  'user.created',
  'user.updated',
  'user.deleted',
  'user.login',
  'user.logout',
  'user.password_changed',

  // outlet actions
  'outlet.created',
  'outlet.updated',
  'outlet.deleted',

  // Sales actions
  'sales.created',
  'sales.updated',
  'sales.deleted',

  // System actions
  'system.backup',
  'system.restore',
  'system.maintenance',
]);

// Predefined resource types for validation
export const resourceTypeSchema = z.enum([
  'user',
  'outlet',
  'sales',
  'system',
]);

// Enhanced create audit log schema with predefined values
export const createAuditLogEnhancedSchema = createAuditLogSchema.extend({
  action: auditActionSchema,
  resourceType: resourceTypeSchema.optional(),
});

// Type exports
export type AuditLogQuery = z.infer<typeof auditLogQuerySchema>;
export type CreateAuditLogRequest = z.infer<typeof createAuditLogSchema>;
export type CreateAuditLogEnhancedRequest = z.infer<typeof createAuditLogEnhancedSchema>;
export type BulkCreateAuditLogsRequest = z.infer<typeof bulkCreateAuditLogsSchema>;
export type AuditLogIdParam = z.infer<typeof auditLogIdParamSchema>;
export type UserIdParam = z.infer<typeof userIdParamSchema>;
export type ResourceAuditLogsParam = z.infer<typeof resourceAuditLogsParamSchema>;
export type AuditLogStatsQuery = z.infer<typeof auditLogStatsQuerySchema>;
export type CleanupOldLogsRequest = z.infer<typeof cleanupOldLogsSchema>;
export type RestoreAuditLogRequest = z.infer<typeof restoreAuditLogSchema>;
export type ExportAuditLogsRequest = z.infer<typeof exportAuditLogsSchema>;

// Validation helper functions
export const validateAuditLogQuery = (data: unknown): AuditLogQuery => {
  return auditLogQuerySchema.parse(data);
};

export const validateCreateAuditLog = (data: unknown): CreateAuditLogRequest => {
  return createAuditLogSchema.parse(data);
};

export const validateAuditLogId = (data: unknown): AuditLogIdParam => {
  return auditLogIdParamSchema.parse(data);
};

export const validateUserId = (data: unknown): UserIdParam => {
  return userIdParamSchema.parse(data);
};

export const validateResourceAuditLogsParam = (data: unknown): ResourceAuditLogsParam => {
  return resourceAuditLogsParamSchema.parse(data);
};

export const validateAuditLogStatsQuery = (data: unknown): AuditLogStatsQuery => {
  return auditLogStatsQuerySchema.parse(data);
};

export const validateCleanupOldLogs = (data: unknown): CleanupOldLogsRequest => {
  return cleanupOldLogsSchema.parse(data);
};

export const validateExportAuditLogs = (data: unknown): ExportAuditLogsRequest => {
  return exportAuditLogsSchema.parse(data);
};
