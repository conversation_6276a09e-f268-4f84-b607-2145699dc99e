import { z } from 'zod';

// Base username validation
const usernameSchema = z
  .string()
  .min(3, 'Username must be at least 3 characters long')
  .max(100, 'Username must be less than 100 characters')
  .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens');

// Email validation
const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .max(255, 'Email must be less than 255 characters')
  .email('Invalid email format');

// Base password validation
const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password must be less than 128 characters')
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
    'Password must contain at least one lowercase letter, one uppercase letter, and one number'
  );

// Full name validation
const fullNameSchema = z
  .string()
  .min(1, 'Full name is required')
  .max(255, 'Full name must be less than 255 characters')
  .regex(/^[a-zA-Z\s'-]+$/, 'Full name can only contain letters, spaces, hyphens, and apostrophes');

// Role validation
const roleSchema = z.enum(['admin', 'staff', 'user'], {
  errorMap: () => ({ message: 'Role must be admin, staff, or user' })
});

// outlet ID validation
const outletIdSchema = z.number().positive('outlet ID must be a positive number').optional();

// Register request validation
export const registerSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
  fullName: fullNameSchema,
  role: roleSchema.default('user'),
  outletId: outletIdSchema,
});

// Login request validation
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

// JWT payload validation
export const jwtPayloadSchema = z.object({
  userId: z.number().positive(),
  username: usernameSchema,
  email: emailSchema,
  role: roleSchema,
  outletId: outletIdSchema,
  iat: z.number().optional(),
  exp: z.number().optional(),
});

// User update validation
export const updateUserSchema = z.object({
  fullName: fullNameSchema.optional(),
  role: roleSchema.optional(),
  outletId: outletIdSchema,
}).partial();

// Change password validation
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
});

// User query parameters validation
export const userQuerySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).default('1'),
  limit: z.string().regex(/^\d+$/).transform(Number).default('10'),
  search: z.string().max(255).optional().transform(val => val === '' ? undefined : val),
  role: z.enum(['admin', 'staff', 'user', 'all']).optional(),
  outletId: z.string().regex(/^\d+$/).transform(Number).optional(),
  sortBy: z.enum(['username', 'email', 'fullName', 'role']).default('username'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
}).transform((data) => ({
  ...data,
  page: Math.max(1, data.page),
  limit: Math.min(100, Math.max(1, data.limit)),
}));

// User ID parameter validation
export const userIdParamSchema = z.object({
  id: z.string().regex(/^\d+$/, 'User ID must be a number').transform(Number),
});

// Admin user creation validation
export const adminCreateUserSchema = z.object({
  username: usernameSchema,
  email: emailSchema,
  password: passwordSchema,
  fullName: fullNameSchema,
  role: roleSchema,
  outletId: outletIdSchema,
});

// Admin user update validation
export const adminUpdateUserSchema = z.object({
  username: usernameSchema.optional(),
  email: emailSchema.optional(),
  fullName: fullNameSchema.optional(),
  role: roleSchema.optional(),
  outletId: outletIdSchema,
  isActive: z.boolean().optional(),
}).partial();

// Bulk user action validation
export const bulkUserActionSchema = z.object({
  action: z.enum(['activate', 'deactivate', 'delete', 'assign_outlet']),
  userIds: z.array(z.number().positive()).min(1, 'At least one user ID is required'),
  outletId: z.number().positive().optional(), // For assign_outlet action
});

// User stats query validation
export const userStatsQuerySchema = z.object({
  userId: z.string().regex(/^\d+$/).transform(Number).optional(),
});

// Type exports
export type RegisterRequest = z.infer<typeof registerSchema>;
export type LoginRequest = z.infer<typeof loginSchema>;
export type JwtPayload = z.infer<typeof jwtPayloadSchema>;
export type UpdateUserRequest = z.infer<typeof updateUserSchema>;
export type ChangePasswordRequest = z.infer<typeof changePasswordSchema>;
export type UserQuery = z.infer<typeof userQuerySchema>;
export type UserIdParam = z.infer<typeof userIdParamSchema>;
export type AdminCreateUserRequest = z.infer<typeof adminCreateUserSchema>;
export type AdminUpdateUserRequest = z.infer<typeof adminUpdateUserSchema>;
export type BulkUserAction = z.infer<typeof bulkUserActionSchema>;
export type UserStatsQuery = z.infer<typeof userStatsQuerySchema>;
