import { z } from 'zod';
import {
  type SafeUser,
  type PaginatedResult,
  type User,
  type GlobalUserStats,
  type IndividualUserStats,
} from '../interfaces/repositories/IUserRepository';
import {
  AdminCreateUserRequest,
  AdminUpdateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  BulkUserAction,
} from './auth.validator';

// Base schemas for reusability
const dateStringSchema = z.string().datetime().optional();

// Period schema with custom period support
const enumPeriodSchema = z.enum([
  'today', 'week', 'month', 'quarter', 'year', '6m', '12m', '24m',
]).default('month');

const customPeriodSchema = z.string()
  .regex(
    /^(\d+)(d|w|m)$/,
    'Invalid period format. Use "30d", "1w", or "6m" patterns',
  ).refine(
    (period) => {
      const match = period.match(/^(\d+)([d|w|m])$/);
      if (!match) return false;
      try {
        const [_, value, unit] = match;
        const numValue = parseInt(value || '0', 10);
        if (unit === 'd') return numValue >= 1 && numValue <= 365;
        if (unit === 'w') return numValue >= 1 && numValue <= 52;
        if (unit === 'm') return numValue >= 1 && numValue <= 12;
        return false;
      } catch {
        return false;
      }
    },
    {
      message: 'Custom period value must be a valid number followed by d, w, or m',
    },
  );

const periodSchema = z.union([enumPeriodSchema, customPeriodSchema]);

const baseDashboardQuerySchema = z.object({
  period: periodSchema.default('month'),
  startDate: z.preprocess(
    (val) => (val ? new Date(String(val)) : undefined),
    dateStringSchema,
  ),
  endDate: z.preprocess(
    (val) => (val ? new Date(String(val)) : undefined),
    dateStringSchema,
  ),
  includeForecasts: z.preprocess(
    (val) => val === 'true' || val === true,
    z.boolean().default(false),
  ),
  rollupPeriod: z.enum(['hourly', 'daily', 'weekly', 'monthly']).optional(),
  grouping: z.enum(['region', 'category', 'product']).optional(),
  topK: z.preprocess(
    (val) => val ? parseInt(String(val), 10) : undefined,
    z.number().int().gte(1).lte(200).optional(),
  ),
  comparisonBasis: z.enum(['same-period', 'previous-period', 'absolute']).optional(),
  limit: z.preprocess(
    (val) => val ? parseInt(String(val), 10) : 10,
    z.number().int().gte(1).lte(100).default(10),
  ),
});

const adminDashboardQuerySchema = baseDashboardQuerySchema.extend({
  globalMetrics: z.boolean().default(false).optional(),
  includeDeleted: z.boolean().default(false).optional(),
  filterBy: z.union([
    z.literal('region'),
    z.literal('category'),
    z.literal('outlet'),
  ]).optional(),
  exclude: z.union([
    z.literal('outliers'),
    z.literal('holidays'),
  ]).optional(),
}).refine(
  (data) => {
    return !data.filterBy || ['region', 'category', 'outlet'].includes(data.filterBy);
  },
  {
    path: ['filterBy'],
    message: 'Invalid filter option. Allowed values: region, category, outlet',
  },
).refine(
  (data) => {
    return !data.exclude || ['outliers', 'holidays'].includes(data.exclude);
  },
  {
    path: ['exclude'],
    message: 'Invalid exclusion option. Allowed values: outliers, holidays',
  },
);

const userDashboardQuerySchema = baseDashboardQuerySchema.extend({
  outletId: z.preprocess(
    (val) => val ? parseInt(String(val), 10) : undefined,
    z.number().int().positive().optional(),
  ),
  chainName: z.string().max(100).optional(),
  userScope: z.enum(['personal', 'outlet', 'chain']).optional(),
  customFilters: z.union([
    z.literal('premium-only'),
    z.literal('popular'),
    z.literal('seasonal'),
  ]).optional(),
  customDateRange: z.object({
    startDate: z.string().optional(),
    endDate: z.string().optional(),
  }).optional(),
}).refine(
  (data) => {
    if (!data.userScope || data.userScope === 'personal') return true;
    if (data.userScope === 'outlet' && !data.outletId) {
      return false;
    }
    if (data.userScope === 'chain' && (!data.chainName || data.chainName.length > 100)) {
      return false;
    }
    return true;
  },
  {
    message: 'Invalid user scope or missing required fields for outlet/chain',
  },
).refine(
  (data) => {
    return !data.customFilters || ['premium-only', 'popular', 'seasonal'].includes(data.customFilters);
  },
  {
    path: ['customFilters'],
    message: 'Invalid custom filter option. Allowed values: premium-only, popular, seasonal',
  },
).refine(
  (data) => {
    // Validate custom date range if provided
    if (data.customDateRange) {
      const { startDate, endDate } = data.customDateRange;
      if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
          return false;
        }
        if (start >= end) {
          return false;
        }
      }
    }
    return true;
  },
  {
    path: ['customDateRange'],
    message: 'Invalid custom date range. Start date must be before end date and both must be valid dates.',
  },
);

const salesBreakdownQuerySchema = baseDashboardQuerySchema.extend({
  breakdownType: z.enum([
    'payment',
    'product',
    'category',
    'time',
    'location',
  ]).default('payment'),
  productCategory: z.enum([
    'food',
    'beverage',
    'appetizer',
    'main',
    'dessert',
  ]).optional(),
  vendorId: z.preprocess(
    (val) => val ? parseInt(String(val), 10) : undefined,
    z.number().int().positive().optional(),
  ),
}).refine(
  (data) => {
    // If breakdownType is 'product' or 'category', productCategory should be provided
    if (['product', 'category'].includes(data.breakdownType) && !data.productCategory) {
      return false;
    }
    return true;
  },
  {
    path: ['productCategory'],
    message: 'Product category is required when breakdown type is "product" or "category"',
  },
).refine(
  (data) => {
    // Validate productCategory values if provided
    if (data.productCategory && !['food', 'beverage', 'appetizer', 'main', 'dessert'].includes(data.productCategory)) {
      return false;
    }
    return true;
  },
  {
    path: ['productCategory'],
    message: 'Invalid product category. Allowed values: food, beverage, appetizer, main, dessert',
  },
).refine(
  (data) => {
    // Validate vendorId if provided
    if (data.vendorId !== undefined && (typeof data.vendorId !== 'number' || data.vendorId <= 0)) {
      return false;
    }
    return true;
  },
  {
    path: ['vendorId'],
    message: 'Vendor ID must be a positive number',
  },
);

const recentActivityQuerySchema = z.object({
  userId: z.number().optional(),
  type: z.enum(['all', 'sale', 'user', 'outlet']).optional().default('all'),
  activityType: z.enum([
    'login',
    'purchase',
    'feedback',
    'report',
    'message',
    'notification',
  ]).optional(),
  limit: z.number().int().gte(1).lte(100).optional().default(10),
}).refine(
  (data) => {
    if (!data.activityType) return true;
    return ['login', 'purchase', 'feedback', 'report', 'message', 'notification'].includes(data.activityType);
  },
  {
    message: 'Invalid activity type. Allowed values: login, purchase, feedback, report, message, notification',
  },
).refine(
  (data) => {
    if (!data.userId) return true;
    if (typeof data.userId !== 'number' || data.userId < 0) {
      return false;
    }
    return true;
  },
  {
    message: 'User ID must be a positive number',
  },
);

export type {
  SafeUser,
  PaginatedResult,
  User,
  GlobalUserStats,
  IndividualUserStats,
  AdminCreateUserRequest,
  AdminUpdateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  BulkUserAction,
};

// Type definitions inferred from schemas
export type AdminDashboardQuery = z.infer<typeof adminDashboardQuerySchema>;
export type UserDashboardQuery = z.infer<typeof userDashboardQuerySchema>;
export type RecentActivityQuery = z.infer<typeof recentActivityQuerySchema>;
export type SalesBreakdownQuery = z.infer<typeof salesBreakdownQuerySchema>;

// Additional dashboard-related types
export interface RecentActivityItem {
  id: string;
  type: 'sale' | 'user' | 'outlet';
  message: string;
  timestamp: string;
  amount?: number;
  outletId?: number;
  outletName?: string | undefined;
  user?: string;
}

export interface DashboardMetrics {
  totalUsers: number;
  totalOutlets: number;
  totalSales: number;
  totalRevenue: number;
  activeUsers: number;
  inactiveUsers: number;
  newUsersThisMonth: number;
  recentSales: Array<{
    id: number;
    amount: number;
    date: string;
    outletName: string;
  }>;
  topOutlets: Array<{
    id: number;
    name: string;
    totalRevenue: number;
    salesCount: number;
  }>;
}

export interface SalesTrendData {
  name: string;
  value: number;
  date: string;
}

export interface OutletPerformance {
  name: string;
  value: number;
  outletId: number;
  salesCount: number;
  averageSale: number;
}

export interface UserActivityData {
  name: string;
  value: number;
  percentage: number;
}

export interface UserDashboardData {
  todaySales: number;
  weeklySales: number;
  monthlySales: number;
  salesTarget: number;
  targetAchievement: number;
  recentSales: Array<{
    id: number;
    amount: number;
    date: string;
    outletName: string;
  }>;
  performance: {
    thisWeek: number;
    lastWeek: number;
    change: number;
    changeDirection: 'up' | 'down' | 'neutral';
  };
}

export {
  adminDashboardQuerySchema,
  userDashboardQuerySchema,
  recentActivityQuerySchema,
  salesBreakdownQuerySchema,
};
