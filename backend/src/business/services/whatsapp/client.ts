import { Boom } from '@hapi/boom';
import makeWASocket, {
  DisconnectReason,
  fetchLatestBaileysVersion,
  makeCacheableSignalKeyStore,
  useMultiFileAuthState,
  WASocket,
  ConnectionState,
} from '@whiskeysockets/baileys';
import { apiLogger } from 'infrastructure/logger/pino';
import { config } from 'infrastructure/config';
import fs from 'fs/promises';
import path from 'path';
import { EventEmitter } from 'events';

class WhatsAppConnectionManager extends EventEmitter {
  private socket?: WASocket;
  private isConnecting = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;

  constructor() {
    super();
    apiLogger.info('WhatsAppConnectionManager created, but not auto-connecting.');
  }

  async lazyInitialize(): Promise<void> {
    if (this.socket || this.isConnecting) {
      return;
    }
    if (!config.whatsapp.enabled) {
      apiLogger.warn('WhatsApp service is disabled.');
      return;
    }
    await this.connect();
  }

  private async connect(): Promise<void> {
    if (this.isConnecting) {
      apiLogger.warn('Connection attempt already in progress.');
      return;
    }
    this.isConnecting = true;
    apiLogger.info('Attempting to connect to WhatsApp...');
    try {
      const { state, saveCreds } = await useMultiFileAuthState(config.whatsapp.sessionDir);
      const { version, isLatest } = await fetchLatestBaileysVersion();
      
      apiLogger.info({ version: version.join('.'), isLatest }, 'Creating WhatsApp socket');
      
      this.socket = makeWASocket({
        version,
        auth: {
          creds: state.creds,
          keys: makeCacheableSignalKeyStore(state.keys, apiLogger.child({ module: 'baileys-keys' })),
        },
        logger: apiLogger.child({ module: 'baileys' }),
        printQRInTerminal: false,
        browser: ['Broku Sales Dashboard', 'Chrome', '1.0.0'],
      });
      
      this.socket.ev.on('creds.update', saveCreds);
      this.socket.ev.on('connection.update', (update) => this.handleConnectionUpdate(update));
      
    } catch (error) {
      apiLogger.error({ error }, 'Failed to create socket');
    } finally {
      this.isConnecting = false;
    }
  }

  private handleConnectionUpdate(update: Partial<ConnectionState>): void {
    const { connection, lastDisconnect, qr } = update;
    apiLogger.info({ connection, error: lastDisconnect?.error?.message }, 'Connection state update');
    
    if (qr) {
      this.emit('qr', qr);
    }

    if (connection === 'close') {
      this.handleDisconnection(lastDisconnect);
    } else if (connection === 'open') {
      apiLogger.info('WhatsApp connection established successfully.');
      this.emit('qr', null); // Clear QR on successful connection
      this.reconnectAttempts = 0;
    }
  }

  private handleDisconnection(lastDisconnect?: { error?: Error }): void {
    const error = lastDisconnect?.error as Boom;
    const statusCode = error?.output?.statusCode;
    
    apiLogger.error({ statusCode, errorMessage: error?.message }, 'WhatsApp connection closed');
    
    switch (statusCode) {
    case DisconnectReason.connectionLost:
    case DisconnectReason.timedOut:
      this.scheduleReconnection();
      break;
    case DisconnectReason.loggedOut:
      this.clearSession().catch(err => apiLogger.error({ err }, 'Failed to clear session after logout'));
      break;
    case DisconnectReason.connectionReplaced:
      apiLogger.warn('Connection replaced, another connection was opened.');
      break;
    case DisconnectReason.restartRequired:
      apiLogger.info('Restart required, attempting to reconnect...');
      this.scheduleReconnection();
      break;
    case DisconnectReason.timedOut:
      apiLogger.warn('Connection timed out, attempting to reconnect...');
      this.scheduleReconnection();
      break;
    default:
      apiLogger.warn({ statusCode }, 'Unhandled disconnection, will not reconnect.');
      this.socket?.end(undefined);
      break;
    }
  }

  private scheduleReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      apiLogger.error('Maximum reconnection attempts reached.');
      return;
    }
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * this.reconnectAttempts;
    apiLogger.info({ attempts: this.reconnectAttempts, delay }, 'Scheduling reconnection...');
    setTimeout(() => this.connect(), delay);
  }

  requestPairingCode(phoneNumber: string): Promise<string> {
    if (!this.socket) {
      throw new Error('Socket not initialized');
    }
    if (this.socket.authState.creds.registered) {
      throw new Error('Device is already paired');
    }
    
    const validatedNumber = this.validatePhoneNumber(phoneNumber);
    return this.socket.requestPairingCode(validatedNumber);
  }

  private validatePhoneNumber(phoneNumber: string): string {
    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.length < 10) {
      throw new Error('Invalid phone number format. It must include the country code.');
    }
    return cleaned;
  }

  async sendMessage(to: string, message: { text: string }): Promise<void> {
    if (!this.socket || this.getConnectionStatus().isConnected === false) {
      throw new Error('WhatsApp is not connected.');
    }
    const validatedId = to.includes('@s.whatsapp.net') ? to : `${to}@s.whatsapp.net`;
    await this.socket.sendMessage(validatedId, { text: message.text });
  }

  async clearSession(): Promise<void> {
    apiLogger.info('Clearing WhatsApp session...');
    this.socket?.end(undefined);
    this.socket = undefined;
    this.reconnectAttempts = 0;
    try {
      await fs.rm(config.whatsapp.sessionDir, { recursive: true, force: true });
      apiLogger.info('Session directory cleared successfully.');
    } catch (error) {
      apiLogger.error({ error }, 'Failed to delete session directory.');
    }
  }

  getConnectionStatus() {
    return {
      isConnected: !!this.socket?.user,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts,
    };
  }
}

export const whatsappManager = new WhatsAppConnectionManager();

export const initializeWhatsApp = () => whatsappManager.lazyInitialize();
export const requestPairingCode = (phoneNumber: string) => whatsappManager.requestPairingCode(phoneNumber);
export const whatsappEmitter = whatsappManager;
export const getConnectionStatus = () => whatsappManager.getConnectionStatus();
export const disconnect = () => whatsappManager.clearSession();