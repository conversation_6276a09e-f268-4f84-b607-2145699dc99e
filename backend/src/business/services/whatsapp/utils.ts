import { SalesSummary } from 'data/models/sales.model';

/**
 * Formats the daily sales summary into a readable WhatsApp message.
 * @param summary - The sales summary data.
 * @param outletName - The name of the outlet.
 * @returns A formatted string for the WhatsApp message.
 */
export function formatDailySalesSummary(summary: SalesSummary, outletName: string): string {
  const today = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return `
*Daily Sales Summary for ${outletName}*
_Date: ${today}_

*Total Sales:* ${summary.totalSales}
*Total Cash:* ${summary.totalCash}
*Total Card:* ${summary.totalCard}
*Total Online Payments:* ${summary.totalOnline}
*Total Discounts:* ${summary.totalDiscounts}
*Number of Transactions:* ${summary.recordCount}

Have a great day!
  `.trim();
}