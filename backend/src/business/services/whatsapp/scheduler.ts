import schedule from 'node-schedule';
import { apiLogger } from 'infrastructure/logger/pino';
import { config } from 'infrastructure/config';
import { salesService } from 'business/services/sales.service';
import { outletService } from 'business/services/outlet.service';
import { formatDailySalesSummary } from './utils';
import { sendMessage } from './whatsapp.service';

/**
 * Schedules a job to send the daily sales summary.
 */
export function scheduleDailySalesSummary() {
  if (!config.whatsapp.enabled) {
    apiLogger.info('WhatsApp messaging is disabled. Daily summary will not be sent.');
    return;
  }

  // Schedule to run every day at 11:00 PM
  schedule.scheduleJob('0 23 * * *', async () => {
    apiLogger.info('Running scheduled job: Send Daily Sales Summary');
    try {
      const outlets = await outletService.getAll({
        page: 1,
        limit: 100,
        sortBy: 'name',
        sortOrder: 'asc',
      });
      const today = new Date().toISOString().split('T')[0];

      for (const outlet of outlets.data) {
        const summary = await salesService.getSummary(today, today, outlet.outletId);
        if (summary.recordCount > 0) {
          const message = formatDailySalesSummary(summary, outlet.name);
          if (config.whatsapp.recipientNumber) {
            await sendMessage(config.whatsapp.recipientNumber, { text: message });
          } else {
            apiLogger.warn(`No recipient number configured for outlet: ${outlet.name}`);
          }
        } else {
          apiLogger.info(`No sales data for ${outlet.name} today. Skipping message.`);
        }
      }
    } catch (error) {
      apiLogger.error('Error sending daily sales summary:', error);
    }
  });

  apiLogger.info('Daily sales summary job scheduled to run at 11:00 PM daily.');
}