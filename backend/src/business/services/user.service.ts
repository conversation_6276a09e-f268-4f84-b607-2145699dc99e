import { userRepository } from '../../data/repositories/user.repository';
import { authService, type AuthService } from '../../business/services/auth.service';
import {
  type UserQuery,
  type AdminCreateUserRequest,
  type AdminUpdateUserRequest,
  type UpdateUserRequest,
  type ChangePasswordRequest,
  type BulkUserAction
} from '../validators/auth.validator';
import { apiLogger } from '../../infrastructure/logger/pino';
import { AppError, NotFoundError, ConflictError, BadRequestError } from '../../infrastructure/errors';
import {
  IUserRepository,
  type SafeUser,
  type UserQueryParams,
  type UpdateUserData,
  type PaginatedResult
} from '../../business/interfaces/repositories/IUserRepository';

export class UserService {
  constructor(
    private readonly userRepo: IUserRepository = userRepository,
    private readonly authSvc: AuthService = authService
  ) { }

  async getAll(query: UserQuery): Promise<PaginatedResult<SafeUser>> {
    apiLogger.info('Fetching users', { query });

    // Validate query parameters
    this.validateQueryParams(query);

    // Convert UserQuery to UserQueryParams
    const queryParams: UserQueryParams = {
      ...(query.role && { role: query.role }),
      ...(query.outletId && { outletId: query.outletId }),
      ...(query.search && { search: query.search }),
      ...(query.sortBy && { sortBy: query.sortBy as 'username' | 'fullName' | 'role' }),
      ...(query.sortOrder && { sortOrder: query.sortOrder }),
      ...(query.page && { page: query.page }),
      ...(query.limit && { limit: query.limit }),
    };

    try {
      const result = await this.userRepo.findAll(queryParams);

      // Convert User domain models to SafeUser for API response
      const safeUsers = result.data.map(user => this.userRepo.toSafeUser(user));

      return {
        data: safeUsers,
        pagination: result.pagination,
      };
    } catch (error) {
      apiLogger.error('Error fetching users:', error);
      throw new AppError('Failed to retrieve users.');
    }
  }

  private validateQueryParams(query: UserQuery): void {
    if (query.page && query.page < 1) {
      throw new BadRequestError('Page must be greater than 0');
    }
    if (query.limit && (query.limit < 1 || query.limit > 100)) {
      throw new BadRequestError('Limit must be between 1 and 100');
    }
  }

  async getById(id: number): Promise<SafeUser> {
    apiLogger.info('Fetching user by ID', { userId: id });
    const user = await this.userRepo.findSafeById(id);
    if (!user) {
      throw new NotFoundError('User not found');
    }
    return user;
  }

  async create(userData: AdminCreateUserRequest): Promise<SafeUser> {
    apiLogger.info('Creating new user', { username: userData.username, email: userData.email });

    const existingUserByUsername = await this.userRepo.findByUsername(userData.username);
    if (existingUserByUsername) {
      throw new ConflictError('User with this username already exists');
    }

    const existingUserByEmail = await this.userRepo.findByEmail(userData.email);
    if (existingUserByEmail) {
      throw new ConflictError('User with this email already exists');
    }

    const result = await this.authSvc.register(userData);
    apiLogger.info('User created successfully', { userId: result.user.userId });
    return result.user;
  }

  async update(id: number, updateData: AdminUpdateUserRequest): Promise<SafeUser> {
    apiLogger.info('Updating user', { userId: id, updateData });

    const existingUser = await this.userRepo.findById(id);
    if (!existingUser) {
      throw new NotFoundError('User not found');
    }

    if (updateData.username && updateData.username !== existingUser.username) {
      const usernameExists = await this.userRepo.usernameExists(updateData.username);
      if (usernameExists) {
        throw new ConflictError('Username already exists');
      }
    }

    if (updateData.email && updateData.email !== existingUser.email) {
      const emailExists = await this.userRepo.emailExists(updateData.email);
      if (emailExists) {
        throw new ConflictError('Email already exists');
      }
    }

    // Convert AdminUpdateUserRequest to UpdateUserData
    const payload: Partial<UpdateUserData> = {
      ...(updateData.username && { username: updateData.username }),
      ...(updateData.email && { email: updateData.email }),
      ...(updateData.fullName && { fullName: updateData.fullName }),
      ...(updateData.role && { role: updateData.role }),
      ...(updateData.outletId !== undefined && { outletId: updateData.outletId }),
    };

    const updatedUser = await this.userRepo.update(id, payload);
    if (!updatedUser) {
      throw new AppError('Failed to update user.');
    }
    apiLogger.info('User updated successfully', { userId: id });
    return updatedUser;
  }

  async delete(id: number, requestingUserId?: number, password?: string): Promise<void> {
    apiLogger.info('Deleting user', { userId: id });

    const existingUser = await this.userRepo.findById(id);
    if (!existingUser) {
      throw new NotFoundError('User not found');
    }

    if (requestingUserId && requestingUserId === id) {
      if (!password) {
        throw new BadRequestError('Password is required for self-deletion');
      }
      const isPasswordValid = await this.authSvc.verifyPassword(password, existingUser.password);
      if (!isPasswordValid) {
        throw new BadRequestError('Incorrect password');
      }
    } else if (requestingUserId && requestingUserId !== id) {
      // Admin deleting another user, no password needed
    } else if (!requestingUserId) {
      // System or other process deleting a user
    }


    const success = await this.userRepo.delete(id);
    if (!success) {
      throw new AppError('Failed to delete user');
    }
    apiLogger.info('User deleted successfully', { userId: id });
  }

  async getProfile(userId: number): Promise<SafeUser> {
    apiLogger.info('Fetching user profile', { userId });
    const user = await this.userRepo.findSafeById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }
    return user;
  }

  async updateProfile(userId: number, updateData: UpdateUserRequest): Promise<SafeUser> {
    apiLogger.info('Updating user profile', { userId });

    // Convert UpdateUserRequest to UpdateUserData
    const payload: Partial<UpdateUserData> = {
      ...(updateData.fullName && { fullName: updateData.fullName }),
    };

    const updatedUser = await this.userRepo.update(userId, payload);
    if (!updatedUser) {
      throw new AppError('Failed to update user profile.');
    }
    apiLogger.info('User profile updated successfully', { userId });
    return updatedUser;
  }

  async changePassword(userId: number, passwords: ChangePasswordRequest): Promise<void> {
    apiLogger.info('Changing user password', { userId });

    const user = await this.userRepo.findById(userId);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    const isCurrentPasswordValid = await this.authSvc.verifyPassword(
      passwords.currentPassword,
      user.password
    );
    if (!isCurrentPasswordValid) {
      throw new BadRequestError('Current password is incorrect');
    }

    const hashedNewPassword = await this.authSvc.hashPassword(passwords.newPassword);
    const success = await this.userRepo.update(userId, { password: hashedNewPassword });
    if (!success) {
      throw new AppError('Failed to change password.');
    }
    apiLogger.info('User password changed successfully', { userId });
  }

  async getByoutlet(outletId: number): Promise<SafeUser[]> {
    apiLogger.info('Fetching users by outlet', { outletId });
    const users = await this.userRepo.findByoutletId(outletId);
    return users.map(user => this.userRepo.toSafeUser(user));
  }

  async getStats(userId?: number): Promise<any> {
    apiLogger.info(`Fetching user statistics for user: ${userId ?? 'global'}`);

    try {
      if (userId) {
        // Fetch individual user statistics
        const numericUserId = Number(userId);
        if (isNaN(numericUserId)) {
          throw new BadRequestError('Invalid user ID');
        }
        const individualStats = await this.userRepo.getIndividualStats(numericUserId);
        if (!individualStats) {
          throw new NotFoundError('User not found');
        }
        return {
          ...individualStats,
          metadata: {
            dataFreshness: new Date(),
          },
        };
      } else {
        // Fetch global statistics
        const globalStats = await this.userRepo.getGlobalStats();
        const { totalUsers, activeUsers } = globalStats;
        const activeUserPercentage = totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0;

        return {
          ...globalStats,
          activeUserRatio: `${activeUserPercentage.toFixed(2)}%`,
          inactiveUserRatio: `${(100 - activeUserPercentage).toFixed(2)}%`,
          metadata: {
            dataFreshness: new Date(),
            calculationPeriod: 'current',
          },
        };
      }
    } catch (error) {
      apiLogger.error('Error fetching user statistics:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('Failed to fetch user statistics');
    }
  }

  async bulkAction(action: BulkUserAction['action'], userIds: number[], outletId?: number): Promise<any> {
    apiLogger.info('Performing bulk action on users', { action, userIds });

    let results = [];
    let successCount = 0;

    for (const userId of userIds) {
      try {
        let success = false;
        switch (action) {
          case 'activate':
            success = !!(await this.userRepo.update(userId, { isActive: true }));
            break;
          case 'deactivate':
            success = !!(await this.userRepo.update(userId, { isActive: false }));
            break;
          case 'delete':
            // Be careful with self-deletion in bulk actions
            success = await this.userRepo.delete(userId);
            break;
          case 'assign_outlet':
            if (outletId) {
              success = !!(await this.userRepo.update(userId, { outletId }));
            } else {
              throw new BadRequestError('outlet ID is required for assign_outlet action');
            }
            break;
        }
        if (success) successCount++;
        results.push({ userId, success, error: null });
      } catch (error) {
        results.push({
          userId,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    apiLogger.info('Bulk action completed', { action, total: userIds.length, successful: successCount });
    return {
      message: `Bulk ${action} completed. ${successCount}/${userIds.length} operations successful.`,
      results,
    };
  }
}

export const userService = new UserService();