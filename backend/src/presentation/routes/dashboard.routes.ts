import express, { Router } from 'express';
import { authMiddleware } from '../middleware/auth';
import { requireAdmin, requireAnyRole } from '../middleware/authorization';
import { noCache } from '../middleware/no-cache';
import { analysisController } from '../controllers/analysis.controller';
import { dashboardController } from '../controllers/dashboard.controller';

const router: express.Router = Router();

// Apply authentication to all dashboard routes
router.use(authMiddleware.authenticate);

/**
 * @route GET /dashboard/admin
 * @desc Get admin dashboard data with comprehensive metrics
 * @access Admin only
 */
router.get('/admin', requireAdmin, noCache, dashboardController.getAdminDashboard);

/**
 * @route GET /dashboard/user
 * @desc Get user dashboard data with personalized metrics
 * @access All authenticated users
 */
router.get('/user', requireAnyRole, noCache, dashboardController.getUserDashboard);

/**
 * @route GET /dashboard/user/sales-trend
 * @desc Get user sales trend data for charts
 * @access All authenticated users
 */
router.get('/user/sales-trend', requireAnyRole, noCache, dashboardController.getUserSalesTrend);

/**
 * @route GET /dashboard/user/sales-breakdown
 * @desc Get user sales breakdown data for charts
 * @access All authenticated users
 */
router.get('/user/sales-breakdown', requireAnyRole, noCache, dashboardController.getUserSalesBreakdown);

/**
 * @route GET /dashboard/overview
 * @desc Get dashboard overview data (analytics)
 * @access Staff and Admin
 */
router.get('/overview', requireAnyRole, noCache, analysisController.getDashboardAnalytics);

/**
 * @route GET /dashboard/activity
 * @desc Get recent activity data
 * @access All authenticated users
 */
router.get('/activity', requireAnyRole, noCache, dashboardController.getRecentActivity);

/**
 * @route GET /dashboard/metrics
 * @desc Get dashboard metrics summary
 * @access Admin and Staff
 */
router.get('/metrics', requireAnyRole, noCache, dashboardController.getDashboardMetrics);

/**
 * @route GET /dashboard/sales-trend
 * @desc Get sales trend data for charts
 * @access All authenticated users
 */
router.get('/sales-trend', requireAnyRole, noCache, dashboardController.getSalesTrend);

export default router;
