import express, { Router } from 'express';
import { salesController } from '../controllers/sales.controller';
import { authMiddleware } from '../middleware/auth';
import {
  requireAdmin,
  requireStaffOrAdmin,
  requireAnyRole,
  applyOutletFilter,
  requireOutletAccess
} from '../middleware/authorization';
import {
  validateRequest,
  validateQuery,
  validateParams
} from '../middleware/validation';
import {
  createSalesSchema,
  createSalesFormSchema,
  updateSalesSchema,
  salesIdParamSchema,
  salesQuerySchema,
  salesReportSchema,
  salesAnalyticsSchema,
  bulkSalesActionSchema,
  salesDuplicateCheckSchema,
} from '../../business/validators/sales.validator';

const router: express.Router = Router();

// Apply authentication to all sales routes
router.use(authMiddleware.authenticate);

/**
 * @route GET /sales
 * @desc Get all sales with pagination and filtering
 * @access All authenticated users (filtered by outlet for non-admins)
 */
router.get(
  '/',
  requireAny<PERSON><PERSON>,
  applyOutletFilter(),
  validateQuery(salesQuerySchema),
  salesController.getAll
);

/**
 * @route GET /sales/summary
 * @desc Get sales summary for date range
 * @access All authenticated users (filtered by outlet for non-admins)
 */
router.get(
  '/summary',
  requireAnyRole,
  applyOutletFilter(),
  salesController.getSummary
);

/**
 * @route GET /sales/report
 * @desc Generate sales report with grouping and analytics
 * @access Staff, Admin (filtered by outlet for non-admins)
 */
router.get(
  '/report',
  requireStaffOrAdmin,
  applyOutletFilter(),
  validateQuery(salesReportSchema),
  salesController.getReport
);

/**
 * @route GET /sales/analytics
 * @desc Get sales analytics with period comparison
 * @access Staff, Admin (filtered by outlet for non-admins)
 */
router.get(
  '/analytics',
  requireStaffOrAdmin,
  applyOutletFilter(),
  validateQuery(salesAnalyticsSchema),
  salesController.getAnalytics
);

/**
 * @route GET /sales/:id
 * @desc Get sales record by ID
 * @access All authenticated users (with outlet access check)
 */
router.get(
  '/:id',
  requireAnyRole,
  validateParams(salesIdParamSchema),
  salesController.getById
);

/**
 * @route POST /sales
 * @desc Create new sales record
 * @access All authenticated users (with outlet access check)
 */
router.post(
  '/',
  requireAnyRole,
  requireOutletAccess(),
  validateRequest(createSalesSchema),
  salesController.create
);

/**
 * @route POST /sales/form
 * @desc Create new sales record from form data (string inputs)
 * @access All authenticated users (with outlet access check)
 */
router.post(
  '/form',
  requireAnyRole,
  requireOutletAccess(),
  validateRequest(createSalesFormSchema),
  salesController.create
);

/**
 * @route PUT /sales/:id
 * @desc Update sales record
 * @access Staff, Admin (with outlet access check)
 */
router.put(
  '/:id',
  requireStaffOrAdmin,
  requireOutletAccess(),
  validateParams(salesIdParamSchema),
  validateRequest(updateSalesSchema),
  salesController.update
);

/**
 * @route DELETE /sales/:id
 * @desc Soft delete sales record
 * @access Staff, Admin (with outlet access check)
 */
router.delete(
  '/:id',
  requireStaffOrAdmin,
  validateParams(salesIdParamSchema),
  salesController.delete
);

/**
 * @route POST /sales/:id/restore
 * @desc Restore soft-deleted sales record
 * @access Admin only
 */
router.post(
  '/:id/restore',
  requireAdmin,
  validateParams(salesIdParamSchema),
  salesController.restore
);

/**
 * @route POST /sales/bulk
 * @desc Bulk operations on sales records (delete, export)
 * @access Staff, Admin (with outlet access check)
 */
router.post(
  '/bulk',
  requireStaffOrAdmin,
  validateRequest(bulkSalesActionSchema),
  salesController.bulkAction
);

/**
 * @route POST /sales/check-duplicate
 * @desc Check if sales record already exists for outlet and date
 * @access All authenticated users (with outlet access check)
 */
router.post(
  '/check-duplicate',
  requireAnyRole,
  requireOutletAccess(),
  validateRequest(salesDuplicateCheckSchema),
  salesController.checkDuplicate
);

export default router;
