import express, { Router } from 'express';
import { authMiddleware } from '../middleware/auth';
import { requireAnyRole } from '../middleware/authorization';
import { healthCheck } from '../../infrastructure/db/utils';
import { AppError } from '../../infrastructure/errors';

const router: express.Router = Router();

/**
 * @route GET /health
 * @desc Check the health of the application
 * @access Public
 */
router.get('/health', async (_req, res, next) => {
  try {
    const health = await healthCheck();
    if (health.status !== 'healthy') {
      throw new AppError('Database connection is not healthy', 503, { health });
    }

    res.status(200).json({
      success: true,
      message: 'Application is healthy',
      data: health,
    });
  } catch (error) {
    next(error);
  }
});

// Apply authentication to all metadata routes
router.use(authMiddleware.authenticate);

/**
 * @route GET /roles
 * @desc Get all user roles
 * @access All authenticated users
 */
router.get('/roles', requireAnyRole, (_req, res) => {
  res.status(200).json({
    success: true,
    message: 'User roles retrieved successfully',
    data: [
      { value: 'admin', label: 'Administrator', description: 'Full system access' },
      { value: 'staff', label: 'Staff Member', description: 'Limited administrative access' },
      { value: 'user', label: 'Regular User', description: 'Basic user access' },
    ],
  });
});

/**
 * @route GET /departments
 * @desc Get all departments
 * @access All authenticated users
 */
router.get('/departments', requireAnyRole, (_req, res) => {
  res.status(200).json({
    success: true,
    message: 'Departments retrieved successfully',
    data: [
      { id: 'IT', name: 'Information Technology', description: 'Technology and systems' },
      { id: 'Operations', name: 'Operations', description: 'Daily operations management' },
      { id: 'Sales', name: 'Sales', description: 'Sales and customer relations' },
      { id: 'HR', name: 'Human Resources', description: 'Human resources management' },
      { id:- 'Finance', name: 'Finance', description: 'Financial management' },
      { id: 'Marketing', name: 'Marketing', description: 'Marketing and promotions' },
    ],
  });
});

export default router;
