import express, { Router } from 'express';
import { analysisController } from '../controllers/analysis.controller';
import { authMiddleware } from '../middleware/auth';
import { requireAdmin, requireStaffOrAdmin } from '../middleware/authorization';

const router: express.Router = Router();

// Apply authentication to all analysis routes
router.use(authMiddleware.authenticate);

/**
 * @route GET /analysis/headquarters
 * @desc Get headquarters analytics
 * @access Admin only
 */
router.get(
  '/hq',
  requireAdmin,
  analysisController.getDashboardAnalytics
);

/**
 * @route GET /analysis/branch/:id
 * @desc Get branch outlet analytics
 * @access Staff, Admin
 */
router.get(
  '/outlet/:id',
  requireStaffOrAdmin,
  analysisController.getBranchAnalytics
);

/**
 * @route GET /analysis/kpi/:id
 * @desc Get KPI progress for a branch
 * @access Staff, Admin
 */
router.get(
  '/kpi/:id',
  analysisController.getKpiProgress
);

/**
 * @route GET /analysis/dashboard
 * @desc Get comprehensive dashboard analytics
 * @access Admin only
 */
router.get(
  '/dashboard',
  requireAdmin,
  analysisController.getDashboardAnalytics
);

export default router;