import { Request, Response, NextFunction } from 'express';
import { analysisService, AnalysisService } from '../../business/services/analysis.service';
import { AnalyticsQuery } from '../../business/validators/analysis.validator';
import { apiLogger } from '../../infrastructure/logger/pino';

export class AnalysisController {
  constructor(private readonly analysisSvc: AnalysisService = analysisService) { }


  getBranchAnalytics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const outletId = parseInt(req.params['id'] as string);
      const result = await this.analysisSvc.getBranchAnalytics({ outletId });

      // The service now throws AppError, so direct error handling is needed
      // The previous 'success' in result check is no longer necessary

      res.status(200).json({
        success: true,
        message: 'Branch outlet analytics retrieved successfully',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getKpiProgress = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const outletId = parseInt(req.params['id'] as string);
      const result = await this.analysisSvc.getKpiProgress({ outletId });

      res.status(200).json({
        success: true,
        message: 'KPI progress retrieved successfully',
        data: result,
      });
    } catch (error) {
      next(error);
    }
  };

  getDashboardAnalytics = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const period = (req.query['period'] as '6m' | '12m' | '24m') || '12m';
      const includeForecasts = req.query['includeForecasts'] === 'true';

      const query: AnalyticsQuery = { period, includeForecasts };
      const result = await this.analysisSvc.getDashboardAnalytics(query);

      // Disable caching for real-time dashboard data
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      res.status(200).json(result);
    } catch (error) {
      apiLogger.error('Error getting dashboard analytics:', error);
      next(error);
    }
  };
}

export const analysisController = new AnalysisController();