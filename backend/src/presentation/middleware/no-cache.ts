import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to disable caching for real-time data endpoints
 * Useful for dashboard endpoints that need fresh data
 */
export const noCache = (req: Request, res: Response, next: NextFunction): void => {
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  });
  next();
};

/**
 * Middleware to set short-term caching (useful for semi-static data)
 * @param maxAge - Cache duration in seconds (default: 60 seconds)
 */
export const shortCache = (maxAge: number = 60) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    res.set({
      'Cache-Control': `public, max-age=${maxAge}`,
    });
    next();
  };
};

/**
 * Middleware to set long-term caching (useful for static data)
 * @param maxAge - Cache duration in seconds (default: 1 hour)
 */
export const longCache = (maxAge: number = 3600) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    res.set({
      'Cache-Control': `public, max-age=${maxAge}`,
    });
    next();
  };
};
