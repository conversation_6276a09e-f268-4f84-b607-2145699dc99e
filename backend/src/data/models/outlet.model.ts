import { mysqlTable, varchar, timestamp, int, boolean, text, index } from 'drizzle-orm/mysql-core';
import { InferSelectModel, InferInsertModel } from 'drizzle-orm';

// Outlet table schema
export const outlets = mysqlTable('outlets', {
  outletId: int('outlet_id').primaryKey().autoincrement(),
  name: varchar('name', { length: 255 }).notNull(),
  address: varchar('address', { length: 500 }).notNull(),
  phoneNumber: varchar('phone_number', { length: 20 }).notNull(),
  openingTime: varchar('opening_time', { length: 10 }).notNull(), // Format: "HH:MM"
  closingTime: varchar('closing_time', { length: 10 }).notNull(), // Format: "HH:MM"
  isActive: boolean('is_active').notNull().default(true),
  description: text('description'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().onUpdateNow().notNull(),
  deletedAt: timestamp('deleted_at'),
}, (table) => ({
  nameIdx: index('name_idx').on(table.name),
  isActiveIdx: index('is_active_idx').on(table.isActive),
  deletedAtIdx: index('deleted_at_idx').on(table.deletedAt),
}));

// Type definitions
export type Outlet = InferSelectModel<typeof outlets>;
export type NewOutlet = InferInsertModel<typeof outlets>;

// Outlet creation payload
export type CreateOutletPayload = Omit<NewOutlet, 'outletId' | 'createdAt' | 'updatedAt' | 'deletedAt'>;

// Outlet update payload
export type UpdateOutletPayload = Partial<Omit<NewOutlet, 'outletId' | 'createdAt' | 'updatedAt' | 'deletedAt'>>;

// Outlet with soft delete support
export type ActiveOutlet = Outlet & { deletedAt: null };
