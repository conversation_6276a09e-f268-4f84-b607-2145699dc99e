import { mysqlTable, varchar, int, text, timestamp, json, index } from 'drizzle-orm/mysql-core';
import { InferSelectModel, InferInsertModel } from 'drizzle-orm';
import { users } from './user.model';

// Audit Log table schema
export const auditLogs = mysqlTable('audit_logs', {
  id: varchar('id', { length: 36 }).primaryKey(), // UUID
  userId: int('user_id').references(() => users.userId),
  action: varchar('action', { length: 100 }).notNull(),
  resourceType: varchar('resource_type', { length: 50 }),
  resourceId: varchar('resource_id', { length: 100 }),
  oldValues: json('old_values'),
  newValues: json('new_values'),
  ipAddress: varchar('ip_address', { length: 45 }), // IPv6 support
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  deletedAt: timestamp('deleted_at'),
}, (table) => ({
  userIdIdx: index('user_id_idx').on(table.userId),
  actionIdx: index('action_idx').on(table.action),
  resourceTypeIdx: index('resource_type_idx').on(table.resourceType),
  resourceIdIdx: index('resource_id_idx').on(table.resourceId),
  createdAtIdx: index('created_at_idx').on(table.createdAt),
  deletedAtIdx: index('deleted_at_idx').on(table.deletedAt),
  userActionIdx: index('user_action_idx').on(table.userId, table.action),
  resourceIdx: index('resource_idx').on(table.resourceType, table.resourceId),
}));

// Type definitions
export type AuditLog = InferSelectModel<typeof auditLogs>;
export type CreateAuditLogPayload = InferInsertModel<typeof auditLogs>;

// Audit log with user information (for joins)
export interface AuditLogWithUser extends AuditLog {
  performedByName?: string | null;
  performedByEmail?: string | null;
}

// Create audit log data interface
export interface CreateAuditLogData {
  id?: string | undefined; // Optional, will be generated if not provided
  userId?: number | null | undefined;
  action: string;
  resourceType?: string | undefined;
  resourceId?: string | undefined;
  oldValues?: Record<string, any> | undefined;
  newValues?: Record<string, any> | undefined;
  ipAddress?: string | undefined;
  userAgent?: string | undefined;
}

// Update audit log data interface (for soft deletes)
export interface UpdateAuditLogData {
  deletedAt?: Date | null;
}

// Audit log query parameters
export interface AuditLogQueryParams {
  search?: string | undefined;
  action?: string | undefined;
  userId?: number | undefined;
  performedBy?: number | undefined;
  resourceType?: string | undefined;
  resourceId?: string | undefined;
  dateFrom?: string | undefined;
  dateTo?: string | undefined;
  page?: number | undefined;
  limit?: number | undefined;
  sortBy?: 'createdAt' | 'action' | 'userId' | undefined;
  sortOrder?: 'asc' | 'desc' | undefined;
  includeDeleted?: boolean | undefined;
}

// Audit log statistics
export interface AuditLogStats {
  totalLogs: number;
  todayLogs: number;
  weekLogs: number;
  monthLogs: number;
  topActions: Array<{
    action: string;
    count: number;
  }>;
  topUsers: Array<{
    userId: number;
    userName: string;
    count: number;
  }>;
}

// Audit log filter options
export interface AuditLogFilterOptions {
  actions: Array<{
    value: string;
    label: string;
    color: string;
  }>;
  users: Array<{
    userId: number;
    userName: string;
  }>;
  resourceTypes: Array<{
    value: string;
    label: string;
  }>;
}

// Common audit actions
export const AUDIT_ACTIONS = {
  // User actions
  USER_CREATED: 'user.created',
  USER_UPDATED: 'user.updated',
  USER_DELETED: 'user.deleted',
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  USER_PASSWORD_CHANGED: 'user.password_changed',

  // outlet actions
  outlet_CREATED: 'outlet.created',
  outlet_UPDATED: 'outlet.updated',
  outlet_DELETED: 'outlet.deleted',

  // Sales actions
  SALES_CREATED: 'sales.created',
  SALES_UPDATED: 'sales.updated',
  SALES_DELETED: 'sales.deleted',

  // System actions
  SYSTEM_BACKUP: 'system.backup',
  SYSTEM_RESTORE: 'system.restore',
  SYSTEM_MAINTENANCE: 'system.maintenance',
} as const;

export type AuditAction = typeof AUDIT_ACTIONS[keyof typeof AUDIT_ACTIONS];

// Resource types
export const RESOURCE_TYPES = {
  USER: 'user',
  outlet: 'outlet',
  SALES: 'sales',
  SYSTEM: 'system',
} as const;

export type ResourceType = typeof RESOURCE_TYPES[keyof typeof RESOURCE_TYPES];
