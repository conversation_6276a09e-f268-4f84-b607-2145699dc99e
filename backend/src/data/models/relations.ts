import { relations } from 'drizzle-orm';
import { users } from './user.model';
import { outlets } from './outlet.model';
import { sales } from './sales.model';
import { auditLogs } from './audit-log.model';

// outlet relations
export const outletsRelations = relations(outlets, ({ many }) => ({
  users: many(users),
  sales: many(sales),
}));

// User relations
export const usersRelations = relations(users, ({ one, many }) => ({
  outlet: one(outlets, {
    fields: [users.outletId],
    references: [outlets.outletId],
  }),
  recordedSales: many(sales),
  auditLogs: many(auditLogs),
}));

// Sales relations
export const salesRelations = relations(sales, ({ one }) => ({
  outlet: one(outlets, {
    fields: [sales.outletId],
    references: [outlets.outletId],
  }),
  recordedByUser: one(users, {
    fields: [sales.recordedByUserId],
    references: [users.userId],
  }),
}));

// Audit Log relations
export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
  user: one(users, {
    fields: [auditLogs.userId],
    references: [users.userId],
  }),
}));
