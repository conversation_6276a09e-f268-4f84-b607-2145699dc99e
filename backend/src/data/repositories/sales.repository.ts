import { eq, and, isNull, desc, asc, gte, lte, sum, count } from 'drizzle-orm';
import { db, type Database } from '../../infrastructure/db/client';
import {
  sales,
  type Sales as SalesEntity,
  type CreateSalesPayload,
  type UpdateSalesPayload
} from '../models/sales.model';
import { dbLogger } from '../../infrastructure/logger/pino';
import { paginate } from '../../infrastructure/db/utils';
import {
  ISalesRepository,
  type Sales,
  type SalesQueryParams,
  type CreateSalesData,
  type UpdateSalesData,
  type SalesSummary,
  type PaginatedResult
} from '../../business/interfaces/repositories/ISalesRepository';

export class SalesRepository implements ISalesRepository {
  constructor(private readonly database: Database = db) { }

  // Entity to domain model mapping
  private mapToSales(entity: SalesEntity): Sales {
    const cash = parseFloat(entity.cashAmount);
    const card = parseFloat(entity.cardAmount);
    const online = parseFloat(entity.onlinePaymentAmount);

    let paymentMethod: 'cash' | 'card' | 'online' | 'multiple' | null = null;
    const methods = [];
    if (cash > 0) methods.push('cash');
    if (card > 0) methods.push('card');
    if (online > 0) methods.push('online');

    if (methods.length === 1) {
      paymentMethod = methods[0] as 'cash' | 'card' | 'online';
    } else if (methods.length > 1) {
      paymentMethod = 'multiple';
    }

    return {
      salesId: entity.salesId,
      outletId: entity.outletId,
      salesDate: entity.salesDate.toString(),
      totalSales: entity.totalSales,
      cashAmount: entity.cashAmount,
      cardAmount: entity.cardAmount,
      onlinePaymentAmount: entity.onlinePaymentAmount,
      paymentMethod,
      totalDiscounts: entity.totalDiscounts,
      notes: entity.notes,
      recordedByUserId: entity.recordedByUserId,
      recordedAt: entity.recordedAt,
      updatedAt: entity.updatedAt,
      deletedAt: entity.deletedAt,
    };
  }

  // private mapToSalesSummary(entity: any): SalesSummary {
  //   return {
  //     totalSales: entity.totalSales || '0.00',
  //     totalCash: entity.totalCash || '0.00',
  //     totalCard: entity.totalCard || '0.00',
  //     totalOnline: entity.totalOnline || '0.00',
  //     totalDiscounts: entity.totalDiscounts || '0.00',
  //     recordCount: entity.recordCount || 0,
  //     averageSales: entity.averageSales || '0.00',
  //   };
  // }

  // Implementation of ISalesRepository interface
  async findAll(query: SalesQueryParams): Promise<PaginatedResult<Sales>> {
    try {
      const conditions = [isNull(sales.deletedAt)]; // Always exclude soft deleted records

      if (query.outletId) {
        conditions.push(eq(sales.outletId, query.outletId));
      }

      if (query.recordedBy) {
        conditions.push(eq(sales.recordedByUserId, query.recordedBy));
      }

      if (query.startDate) {
        conditions.push(gte(sales.salesDate, new Date(query.startDate)));
      }

      if (query.endDate) {
        conditions.push(lte(sales.salesDate, new Date(query.endDate)));
      }

      const sortableColumns = {
        salesDate: sales.salesDate,
        totalSales: sales.totalSales,
        recordedAt: sales.recordedAt,
      };
      const sortColumn = sortableColumns[query.sortBy || 'salesDate'];

      const mainQuery = this.database
        .select()
        .from(sales)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(
          query.sortOrder === 'desc'
            ? desc(sortColumn)
            : asc(sortColumn)
        );

      const countQuery = this.database
        .select({ count: count() })
        .from(sales)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      const result = await paginate(mainQuery, countQuery, {
        page: query.page || 1,
        limit: query.limit || 10,
      });

      return {
        data: (result.data as SalesEntity[]).map(entity => this.mapToSales(entity)),
        pagination: result.pagination,
      };
    } catch (error) {
      dbLogger.error('Error finding all sales:', error);
      throw error;
    }
  }

  async create(salesData: CreateSalesData): Promise<Sales> {
    try {
      dbLogger.info('Creating new sales record', {
        outletId: salesData.outletId,
        salesDate: salesData.salesDate
      });

      // Convert CreateSalesData to CreateSalesPayload
      const payload: CreateSalesPayload = {
        outletId: salesData.outletId,
        salesDate: salesData.salesDate,
        totalSales: salesData.totalSales,
        cashAmount: salesData.cashAmount,
        cardAmount: salesData.cardAmount,
        onlinePaymentAmount: salesData.onlinePaymentAmount,
        totalDiscounts: salesData.totalDiscounts,
        notes: salesData.notes,
        recordedByUserId: salesData.recordedByUserId,
      };

      const result = await this.database
        .insert(sales)
        .values(payload);

      const insertId = result[0].insertId;
      const createdSales = await this.findById(Number(insertId));
      if (!createdSales) {
        throw new Error('Failed to retrieve created sales record');
      }

      dbLogger.info('Sales record created successfully', { salesId: createdSales.salesId });
      return createdSales;
    } catch (error) {
      dbLogger.error('Error creating sales record:', error);
      throw error;
    }
  }

  async findById(id: number): Promise<Sales | null> {
    try {
      const [salesRecord] = await this.database
        .select()
        .from(sales)
        .where(eq(sales.salesId, id))
        .limit(1);

      return salesRecord ? this.mapToSales(salesRecord) : null;
    } catch (error) {
      dbLogger.error('Error finding sales record by ID:', error);
      throw error;
    }
  }

  async findActiveById(id: number): Promise<Sales | null> {
    try {
      const [salesRecord] = await this.database
        .select()
        .from(sales)
        .where(and(
          eq(sales.salesId, id),
          isNull(sales.deletedAt)
        ))
        .limit(1);

      return salesRecord ? this.mapToSales(salesRecord) : null;
    } catch (error) {
      dbLogger.error('Error finding active sales record by ID:', error);
      throw error;
    }
  }

  async findByoutletId(outletId: number, limit?: number): Promise<Sales[]> {
    try {
      const queryBuilder = this.database
        .select()
        .from(sales)
        .where(eq(sales.outletId, outletId))
        .orderBy(desc(sales.salesDate), desc(sales.recordedAt));

      if (limit) {
        (queryBuilder as any).limit(limit);
      }

      const salesList = await queryBuilder;
      return salesList.map(entity => this.mapToSales(entity));
    } catch (error) {
      dbLogger.error('Error finding sales by outlet ID:', error);
      throw error;
    }
  }

  async findByDateRange(startDate: string, endDate: string, outletId?: number): Promise<Sales[]> {
    try {
      const conditions = [
        gte(sales.salesDate, new Date(startDate)),
        lte(sales.salesDate, new Date(endDate)),
        isNull(sales.deletedAt)
      ];

      if (outletId) {
        conditions.push(eq(sales.outletId, outletId));
      }

      const salesList = await this.database
        .select()
        .from(sales)
        .where(and(...conditions))
        .orderBy(desc(sales.salesDate), asc(sales.outletId));

      return salesList.map(entity => this.mapToSales(entity));
    } catch (error) {
      dbLogger.error('Error finding sales by date range:', error);
      throw error;
    }
  }

  async findByRecordedUser(userId: number, limit?: number): Promise<Sales[]> {
    try {
      const queryBuilder = this.database
        .select()
        .from(sales)
        .where(and(
          eq(sales.recordedByUserId, userId),
          isNull(sales.deletedAt)
        ))
        .orderBy(desc(sales.recordedAt));

      if (limit) {
        (queryBuilder as any).limit(limit);
      }

      const salesList = await queryBuilder;
      return salesList.map(entity => this.mapToSales(entity));
    } catch (error) {
      dbLogger.error('Error finding sales by recorded user:', error);
      throw error;
    }
  }

  async getSalesSummary(startDate: string, endDate: string, outletId?: number): Promise<SalesSummary> {
    try {
      const conditions = [
        gte(sales.salesDate, new Date(startDate)),
        lte(sales.salesDate, new Date(endDate)),
        isNull(sales.deletedAt)
      ];

      if (outletId) {
        conditions.push(eq(sales.outletId, outletId));
      }

      const [summary] = await this.database
        .select({
          totalSales: sum(sales.totalSales),
          totalCash: sum(sales.cashAmount),
          totalCard: sum(sales.cardAmount),
          totalOnline: sum(sales.onlinePaymentAmount),
          totalDiscounts: sum(sales.totalDiscounts),
          recordCount: count(sales.salesId)
        })
        .from(sales)
        .where(and(...conditions));

      // Calculate average sales
      const totalSalesValue = parseFloat(summary?.totalSales || '0');
      const recordCount = summary?.recordCount || 0;
      const averageSales = recordCount > 0 ? (totalSalesValue / recordCount).toFixed(2) : '0.00';

      return {
        totalSales: summary?.totalSales || '0.00',
        totalCash: summary?.totalCash || '0.00',
        totalCard: summary?.totalCard || '0.00',
        totalOnline: summary?.totalOnline || '0.00',
        totalDiscounts: summary?.totalDiscounts || '0.00',
        recordCount: summary?.recordCount || 0,
        averageSales: averageSales
      };
    } catch (error) {
      dbLogger.error('Error getting sales summary:', error);
      throw error;
    }
  }

  async update(id: number, data: Partial<UpdateSalesData>): Promise<Sales | null> {
    try {
      dbLogger.info('Updating sales record', { salesId: id });

      // Convert UpdateSalesData to database format
      const updateData: Partial<UpdateSalesPayload> = {};
      if (data.salesDate !== undefined) updateData.salesDate = data.salesDate;
      if (data.totalSales !== undefined) updateData.totalSales = data.totalSales;
      if (data.cashAmount !== undefined) updateData.cashAmount = data.cashAmount;
      if (data.cardAmount !== undefined) updateData.cardAmount = data.cardAmount;
      if (data.onlinePaymentAmount !== undefined) updateData.onlinePaymentAmount = data.onlinePaymentAmount;
      if (data.totalDiscounts !== undefined) updateData.totalDiscounts = data.totalDiscounts;
      if (data.notes !== undefined) updateData.notes = data.notes;

      await this.database
        .update(sales)
        .set(updateData)
        .where(eq(sales.salesId, id));

      const updatedSales = await this.findById(id);
      if (updatedSales) {
        dbLogger.info('Sales record updated successfully', { salesId: id });
      }

      return updatedSales;
    } catch (error) {
      dbLogger.error('Error updating sales record:', error);
      throw error;
    }
  }

  async softDelete(id: number): Promise<boolean> {
    try {
      dbLogger.info('Soft deleting sales record', { salesId: id });

      const result = await this.database
        .update(sales)
        .set({ deletedAt: new Date() })
        .where(eq(sales.salesId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('Sales record soft deleted successfully', { salesId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error soft deleting sales record:', error);
      throw error;
    }
  }

  async restore(id: number): Promise<boolean> {
    try {
      dbLogger.info('Restoring sales record', { salesId: id });

      const result = await this.database
        .update(sales)
        .set({ deletedAt: null })
        .where(eq(sales.salesId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('Sales record restored successfully', { salesId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error restoring sales record:', error);
      throw error;
    }
  }

  async hardDelete(id: number): Promise<boolean> {
    try {
      dbLogger.info('Hard deleting sales record', { salesId: id });

      const result = await this.database
        .delete(sales)
        .where(eq(sales.salesId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('Sales record hard deleted successfully', { salesId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error hard deleting sales record:', error);
      throw error;
    }
  }

  async findDuplicateEntry(outletId: number, salesDate: string): Promise<Sales | null> {
    try {
      const [existingSales] = await this.database
        .select()
        .from(sales)
        .where(and(
          eq(sales.outletId, outletId),
          eq(sales.salesDate, new Date(salesDate)),
          isNull(sales.deletedAt)
        ))
        .limit(1);

      return existingSales ? this.mapToSales(existingSales) : null;
    } catch (error) {
      dbLogger.error('Error checking for duplicate sales entry:', error);
      throw error;
    }
  }
}

export const salesRepository = new SalesRepository();
