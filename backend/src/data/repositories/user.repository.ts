import { eq, and, like, or, count, desc, asc, ne, sql } from 'drizzle-orm';
import { db, type Database } from '../../infrastructure/db/client';
import { users, type User as UserEntity, type CreateUserPayload } from '../models/user.model';
import { dbLogger } from '../../infrastructure/logger/pino';
import { paginate } from '../../infrastructure/db/utils';
import {
  IUserRepository,
  type User,
  type SafeUser,
  type UserQueryParams,
  type CreateUserData,
  type UpdateUserData,
  type PaginatedResult,
  type GlobalUserStats,
  type IndividualUserStats,
} from '../../business/interfaces/repositories/IUserRepository';

export class UserRepository implements IUserRepository {
  constructor(private readonly database: Database = db) { }

  // Entity to domain model mapping
  private mapToUser(entity: UserEntity): User {
    return {
      userId: entity.userId,
      username: entity.username,
      email: entity.email,
      password: entity.password,
      fullName: entity.fullName,
      role: entity.role,
      outletId: entity.outletId,
      isActive: entity.isActive,
      lastLoginAt: entity.lastLoginAt,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  // private mapToSafeUser(entity: UserEntity): SafeUser {
  //   const user = this.mapToUser(entity);
  //   const { password: _password, ...safeUser } = user;
  //   return safeUser;
  // }

  // Implementation of IUserRepository interface
  async findAll(query: UserQueryParams): Promise<PaginatedResult<User>> {
    try {
      const conditions = [];
      // Only apply role filter if role is specified and not 'all'
      if (query.role && query.role !== 'all') {
        conditions.push(eq(users.role, query.role as any));
      }
      if (query.outletId) {
        conditions.push(eq(users.outletId, query.outletId));
      }
      if (query.search) {
        conditions.push(
          or(
            like(users.username, `%${query.search}%`),
            like(users.email, `%${query.search}%`),
            like(users.fullName, `%${query.search}%`)
          )
        );
      }

      const sortableColumns = {
        username: users.username,
        email: users.email,
        fullName: users.fullName,
        role: users.role,
      };
      const sortColumn = sortableColumns[query.sortBy || 'username'];

      const mainQuery = this.database
        .select()
        .from(users)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(
          query.sortOrder === 'desc'
            ? desc(sortColumn)
            : asc(sortColumn)
        );

      const countQuery = this.database
        .select({ count: count() })
        .from(users)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      const result = await paginate(mainQuery, countQuery, {
        page: query.page || 1,
        limit: query.limit || 10,
      });

      return {
        data: (result.data as UserEntity[]).map(entity => this.mapToUser(entity)),
        pagination: result.pagination,
      };
    } catch (error) {
      dbLogger.error('Error finding all users:', error);
      throw error;
    }
  }

  async create(userData: CreateUserData): Promise<SafeUser> {
    try {
      dbLogger.info('Creating new user', { username: userData.username, email: userData.email });

      // Convert CreateUserData to CreateUserPayload
      const payload: CreateUserPayload = {
        username: userData.username,
        email: userData.email,
        password: userData.password,
        fullName: userData.fullName,
        role: userData.role as 'admin' | 'staff' | 'user',
        outletId: userData.outletId,
      };

      const result = await this.database
        .insert(users)
        .values(payload);

      const insertId = result[0].insertId;
      const createdUser = await this.findById(Number(insertId));
      if (!createdUser) {
        throw new Error('Failed to retrieve created user');
      }

      dbLogger.info('User created successfully', { userId: createdUser.userId });
      return this.toSafeUser(createdUser);
    } catch (error) {
      dbLogger.error('Error creating user:', error);
      throw error;
    }
  }

  async findById(id: number): Promise<User | null> {
    try {
      const [user] = await this.database
        .select()
        .from(users)
        .where(eq(users.userId, id))
        .limit(1);

      return user ? this.mapToUser(user) : null;
    } catch (error) {
      dbLogger.error('Error finding user by ID:', error);
      throw error;
    }
  }

  async findByUsername(username: string): Promise<User | null> {
    try {
      const [user] = await this.database
        .select()
        .from(users)
        .where(eq(users.username, username))
        .limit(1);

      return user ? this.mapToUser(user) : null;
    } catch (error) {
      dbLogger.error('Error finding user by username:', error);
      throw error;
    }
  }

  async findByEmail(email: string): Promise<User | null> {
    try {
      const [user] = await this.database
        .select()
        .from(users)
        .where(eq(users.email, email))
        .limit(1);

      return user ? this.mapToUser(user) : null;
    } catch (error) {
      dbLogger.error('Error finding user by email:', error);
      throw error;
    }
  }

  async findByoutletId(outletId: number): Promise<User[]> {
    try {
      const userList = await this.database
        .select()
        .from(users)
        .where(eq(users.outletId, outletId));

      return userList.map(user => this.mapToUser(user));
    } catch (error) {
      dbLogger.error('Error finding users by outlet ID:', error);
      throw error;
    }
  }

  async findSafeById(id: number): Promise<SafeUser | null> {
    const user = await this.findById(id);
    return user ? this.toSafeUser(user) : null;
  }

  async findSafeByUsername(username: string): Promise<SafeUser | null> {
    const user = await this.findByUsername(username);
    return user ? this.toSafeUser(user) : null;
  }

  async usernameExists(username: string, excludeId?: number): Promise<boolean> {
    try {
      const conditions = [eq(users.username, username)];

      if (excludeId) {
        // If excludeId is provided, we want to exclude that user from the check
        conditions.push(ne(users.userId, excludeId));
      }

      const [user] = await this.database
        .select()
        .from(users)
        .where(conditions.length > 1 ? and(...conditions) : conditions[0])
        .limit(1);

      return !!user;
    } catch (error) {
      dbLogger.error('Error checking username existence:', error);
      throw error;
    }
  }

  async emailExists(email: string, excludeId?: number): Promise<boolean> {
    try {
      const conditions = [eq(users.email, email)];

      if (excludeId) {
        // If excludeId is provided, we want to exclude that user from the check
        conditions.push(ne(users.userId, excludeId));
      }

      const [user] = await this.database
        .select()
        .from(users)
        .where(conditions.length > 1 ? and(...conditions) : conditions[0])
        .limit(1);

      return !!user;
    } catch (error) {
      dbLogger.error('Error checking email existence:', error);
      throw error;
    }
  }

  async update(id: number, data: Partial<UpdateUserData>): Promise<SafeUser | null> {
    try {
      dbLogger.info('Updating user', { userId: id });

      // Convert UpdateUserData to database format and filter out undefined values
      const updateData: Partial<Omit<CreateUserPayload, 'userId'>> = {};
      if (data.username !== undefined) updateData.username = data.username;
      if (data.email !== undefined) updateData.email = data.email;
      if (data.password !== undefined) updateData.password = data.password;
      if (data.fullName !== undefined) updateData.fullName = data.fullName;
      if (data.role !== undefined) updateData.role = data.role as 'admin' | 'staff' | 'user';
      if (data.outletId !== undefined) updateData.outletId = data.outletId;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;
      if (data.lastLoginAt !== undefined) updateData.lastLoginAt = data.lastLoginAt;

      await this.database
        .update(users)
        .set(updateData)
        .where(eq(users.userId, id));

      const updatedUser = await this.findById(id);
      if (updatedUser) {
        dbLogger.info('User updated successfully', { userId: id });
        return this.toSafeUser(updatedUser);
      }

      return null;
    } catch (error) {
      dbLogger.error('Error updating user:', error);
      throw error;
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      dbLogger.info('Deleting user', { userId: id });

      const result = await this.database
        .delete(users)
        .where(eq(users.userId, id));

      const success = result[0].affectedRows > 0;
      if (success) {
        dbLogger.info('User deleted successfully', { userId: id });
      }

      return success;
    } catch (error) {
      dbLogger.error('Error deleting user:', error);
      throw error;
    }
  }

  toSafeUser(user: User): SafeUser {
    const { password: _password, ...safeUser } = user;
    return {
      ...safeUser,
      role: safeUser.role as 'admin' | 'staff' | 'user'
    };
  }

  async getGlobalStats(): Promise<GlobalUserStats> {
    try {
      dbLogger.info('Fetching global user statistics from the database');

      const totalUsersQuery = this.database.select({ count: count() }).from(users);
      const activeUsersQuery = this.database.select({ count: count() }).from(users).where(eq(users.isActive, true));

      const newUsersThisMonthQuery = this.database
        .select({ count: count() })
        .from(users)
        .where(sql`created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')`);

      const usersByRoleQuery = this.database
        .select({
          role: users.role,
          count: count(),
        })
        .from(users)
        .groupBy(users.role);

      const [
        totalUsersResult,
        activeUsersResult,
        newUsersThisMonthResult,
        usersByRoleResult,
      ] = await Promise.all([
        totalUsersQuery,
        activeUsersQuery,
        newUsersThisMonthQuery,
        usersByRoleQuery,
      ]);

      const totalUsers = totalUsersResult[0]?.count ?? 0;
      const activeUsers = activeUsersResult[0]?.count ?? 0;
      const newUsersThisMonth = newUsersThisMonthResult[0]?.count ?? 0;
      const usersByRole = usersByRoleResult.reduce((acc, row) => {
        acc[row.role] = row.count;
        return acc;
      }, {} as Record<string, number>);

      return {
        totalUsers,
        activeUsers,
        inactiveUsers: totalUsers - activeUsers,
        newUsersThisMonth,
        usersByRole,
      };
    } catch (error) {
      dbLogger.error('Error fetching global user statistics:', error);
      throw error;
    }
  }

  async getIndividualStats(userId: number): Promise<IndividualUserStats | null> {
    try {
      dbLogger.info(`Fetching statistics for user ${userId}`);

      const user = await this.findById(userId);
      if (!user) {
        return null;
      }

      // These are just placeholders for now
      const sessionCount = 120;
      const avgSessionDuration = 25;

      return {
        userId: user.userId,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt,
        sessionCount,
        avgSessionDuration,
      };
    } catch (error) {
      dbLogger.error(`Error fetching statistics for user ${userId}:`, error);
      throw error;
    }
  }
}

export const userRepository = new UserRepository();
