{"info": {"_postman_id": "broku-sales-dashboard-api-v2", "name": "Broku Sales Dashboard API v2", "description": "Updated Postman Collection for the Broku Sales Dashboard API. Includes all current endpoints: Authentication, Users, Outlets, Sales, Dashboard, Analysis, Audit Logs, and System endpoints. Uses Bearer Token authentication.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": {"major": 2, "minor": 0, "patch": 0}}, "variables": [{"key": "baseUrl", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "token", "value": "YOUR_AUTH_TOKEN", "type": "string"}, {"key": "outletId", "value": "YOUR_OUTLET_ID", "type": "string"}, {"key": "userId", "value": "YOUR_USER_ID", "type": "string"}, {"key": "userId1", "value": "YOUR_USER_ID_1", "type": "string"}, {"key": "userId2", "value": "YOUR_USER_ID_2", "type": "string"}, {"key": "outletId1", "value": "YOUR_OUTLET_ID_1", "type": "string"}, {"key": "outletId2", "value": "YOUR_OUTLET_ID_2", "type": "string"}, {"key": "salesId", "value": "YOUR_SALES_ID", "type": "string"}, {"key": "salesId1", "value": "YOUR_SALES_ID_1", "type": "string"}, {"key": "salesId2", "value": "YOUR_SALES_ID_2", "type": "string"}, {"key": "auditLogId", "value": "YOUR_AUDIT_LOG_ID", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"role\": \"user\",\n  \"outletId\": \"{{outletId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}, "description": "Register a new user."}, "response": []}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Login an existing user and get a token."}, "response": [], "event": [{"listen": "test", "script": {"exec": ["var jsonData = pm.response.json();", "if (jsonData && jsonData.data && jsonData.data.token) {", "    pm.collectionVariables.set(\"token\", jsonData.data.token);", "}"], "type": "text/javascript"}}]}, {"name": "Verify <PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/auth/verify", "host": ["{{baseUrl}}"], "path": ["auth", "verify"]}, "description": "Verify the authentication token."}, "response": []}, {"name": "Get Current User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/auth/me", "host": ["{{baseUrl}}"], "path": ["auth", "me"]}, "description": "Get current user profile (protected endpoint)."}, "response": []}]}, {"name": "Users", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users?page=1&limit=10&role=user", "host": ["{{baseUrl}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "role", "value": "user"}]}, "description": "Get all users (Admin only)."}, "response": []}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/profile", "host": ["{{baseUrl}}"], "path": ["users", "profile"]}, "description": "Get current user profile."}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"updateduser\",\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/users/profile", "host": ["{{baseUrl}}"], "path": ["users", "profile"]}, "description": "Update current user profile."}, "response": []}, {"name": "Change User Password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"oldPassword\": \"password123\",\n  \"newPassword\": \"newpassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/users/change-password", "host": ["{{baseUrl}}"], "path": ["users", "change-password"]}, "description": "Change current user password."}, "response": []}, {"name": "Get Users by Outlet", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/outlet/{{outletId}}", "host": ["{{baseUrl}}"], "path": ["users", "outlet", "{{outletId}}"]}, "description": "Get users by outlet (Staff, Admin)."}, "response": []}, {"name": "Get User Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/stats", "host": ["{{baseUrl}}"], "path": ["users", "stats"]}, "description": "Get user statistics (Admin only)."}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["users", "{{userId}}"]}, "description": "Get user by ID (Admin or self)."}, "response": []}, {"name": "Create New User (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newadminuser\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"adminpass\",\n  \"role\": \"admin\",\n  \"outletId\": \"{{outletId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}, "description": "Create a new user (Admin only)."}, "response": []}, {"name": "Update User (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"updatedadminuser\",\n  \"email\": \"<EMAIL>\",\n  \"role\": \"staff\"\n}"}, "url": {"raw": "{{baseUrl}}/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["users", "{{userId}}"]}, "description": "Update user details (Admin only)."}, "response": []}, {"name": "Delete User (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["users", "{{userId}}"]}, "description": "Delete a user (Admin only)."}, "response": []}, {"name": "Bulk User Action (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"deactivate\",\n  \"userIds\": [\"{{userId1}}\", \"{{userId2}}\"]\n}"}, "url": {"raw": "{{baseUrl}}/users/bulk", "host": ["{{baseUrl}}"], "path": ["users", "bulk"]}, "description": "Perform bulk actions on users (Admin only)."}, "response": []}]}, {"name": "Outlets", "item": [{"name": "Get All Outlets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/outlets?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["outlets"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get all outlets with pagination and filtering (Staff, Admin)."}, "response": []}, {"name": "Get Active Outlets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/outlets/active", "host": ["{{baseUrl}}"], "path": ["outlets", "active"]}, "description": "Get only active outlets (Staff, Admin)."}, "response": []}, {"name": "Search Outlets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/outlets/search?query=cafe", "host": ["{{baseUrl}}"], "path": ["outlets", "search"], "query": [{"key": "query", "value": "cafe"}]}, "description": "Search outlets by name, address, or description (Staff, Admin)."}, "response": []}, {"name": "Get Outlet by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/outlets/{{outletId}}", "host": ["{{baseUrl}}"], "path": ["outlets", "{{outletId}}"]}, "description": "Get outlet by ID (Staff, Admin)."}, "response": []}, {"name": "Create New Outlet (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"New Outlet\",\n  \"address\": \"123 Main St\",\n  \"contactEmail\": \"<EMAIL>\",\n  \"contactPhone\": \"************\"\n}"}, "url": {"raw": "{{baseUrl}}/outlets", "host": ["{{baseUrl}}"], "path": ["outlets"]}, "description": "Create a new outlet (Admin only)."}, "response": []}, {"name": "Update Outlet (Admin)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Outlet Name\",\n  \"address\": \"456 Oak Ave\"\n}"}, "url": {"raw": "{{baseUrl}}/outlets/{{outletId}}", "host": ["{{baseUrl}}"], "path": ["outlets", "{{outletId}}"]}, "description": "Update outlet details (Admin only)."}, "response": []}, {"name": "Update Outlet Status (Admin)", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"isActive\": true\n}"}, "url": {"raw": "{{baseUrl}}/outlets/{{outletId}}/status", "host": ["{{baseUrl}}"], "path": ["outlets", "{{outletId}}", "status"]}, "description": "Update outlet status (active/inactive) (Admin only)."}, "response": []}, {"name": "Soft Delete Outlet (Admin)", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/outlets/{{outletId}}", "host": ["{{baseUrl}}"], "path": ["outlets", "{{outletId}}"]}, "description": "Soft delete a outlet (Admin only)."}, "response": []}, {"name": "<PERSON><PERSON> Outlet (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/outlets/{{outletId}}/restore", "host": ["{{baseUrl}}"], "path": ["outlets", "{{outletId}}", "restore"]}, "description": "Restore a soft-deleted outlet (Admin only)."}, "response": []}, {"name": "Bulk Outlet Action (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"deactivate\",\n  \"outletIds\": [\"{{outletId1}}\", \"{{outletId2}}\"]\n}"}, "url": {"raw": "{{baseUrl}}/outlets/bulk", "host": ["{{baseUrl}}"], "path": ["outlets", "bulk"]}, "description": "Perform bulk actions on outlets (Admin only)."}, "response": []}]}, {"name": "Sales", "item": [{"name": "Get All Sales", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/sales?page=1&limit=10&restaurantId={{restaurantId}}", "host": ["{{baseUrl}}"], "path": ["sales"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "restaurantId", "value": "{{restaurantId}}"}]}, "description": "Get all sales with pagination and filtering."}, "response": []}, {"name": "Get Sales Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/sales/summary?startDate=2024-01-01&endDate=2024-01-31&restaurantId={{restaurantId}}", "host": ["{{baseUrl}}"], "path": ["sales", "summary"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}, {"key": "restaurantId", "value": "{{restaurantId}}"}]}, "description": "Get sales summary for a date range."}, "response": []}, {"name": "Generate Sales Report", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/sales/report?startDate=2024-01-01&endDate=2024-01-31&groupBy=day&restaurantId={{restaurantId}}", "host": ["{{baseUrl}}"], "path": ["sales", "report"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-01-31"}, {"key": "groupBy", "value": "day"}, {"key": "restaurantId", "value": "{{restaurantId}}"}]}, "description": "Generate sales report with grouping and analytics (Staff, Admin)."}, "response": []}, {"name": "Get Sales Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/sales/analytics?period=month&comparePeriod=lastMonth&restaurantId={{restaurantId}}", "host": ["{{baseUrl}}"], "path": ["sales", "analytics"], "query": [{"key": "period", "value": "month"}, {"key": "comparePeriod", "value": "lastM<PERSON>h"}, {"key": "restaurantId", "value": "{{restaurantId}}"}]}, "description": "Get sales analytics with period comparison (Staff, Admin)."}, "response": []}, {"name": "Get Sales Record by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/sales/{{salesId}}", "host": ["{{baseUrl}}"], "path": ["sales", "{{salesId}}"]}, "description": "Get sales record by ID."}, "response": []}, {"name": "Create New Sales Record", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"restaurantId\": \"{{restaurantId}}\",\n  \"salesDate\": \"2024-06-28T00:00:00.000Z\",\n  \"totalSales\": 1500.75,\n  \"transactionCount\": 120\n}"}, "url": {"raw": "{{baseUrl}}/sales", "host": ["{{baseUrl}}"], "path": ["sales"]}, "description": "Create a new sales record."}, "response": []}, {"name": "Create New Sales Record from Form", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"restaurantId\": \"{{restaurantId}}\",\n  \"salesDate\": \"2024-06-28\",\n  \"totalSales\": \"1500.75\",\n  \"transactionCount\": \"120\"\n}"}, "url": {"raw": "{{baseUrl}}/sales/form", "host": ["{{baseUrl}}"], "path": ["sales", "form"]}, "description": "Create a new sales record from form data (string inputs)."}, "response": []}, {"name": "Update Sales Record", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"totalSales\": 1600.00,\n  \"transactionCount\": 130\n}"}, "url": {"raw": "{{baseUrl}}/sales/{{salesId}}", "host": ["{{baseUrl}}"], "path": ["sales", "{{salesId}}"]}, "description": "Update a sales record (Staff, Admin)."}, "response": []}, {"name": "Soft Delete Sales Record", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/sales/{{salesId}}", "host": ["{{baseUrl}}"], "path": ["sales", "{{salesId}}"]}, "description": "Soft delete a sales record (Staff, Admin)."}, "response": []}, {"name": "Restore Sales Record (Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/sales/{{salesId}}/restore", "host": ["{{baseUrl}}"], "path": ["sales", "{{salesId}}", "restore"]}, "description": "Restore a soft-deleted sales record (Admin only)."}, "response": []}, {"name": "Bulk Sales Action (Staff, Admin)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"delete\",\n  \"salesIds\": [\"{{salesId1}}\", \"{{salesId2}}\"]\n}"}, "url": {"raw": "{{baseUrl}}/sales/bulk", "host": ["{{baseUrl}}"], "path": ["sales", "bulk"]}, "description": "Perform bulk actions on sales records (Staff, Admin)."}, "response": []}, {"name": "Check Duplicate Sales", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"restaurantId\": \"{{restaurantId}}\",\n  \"salesDate\": \"2024-06-28\"\n}"}, "url": {"raw": "{{baseUrl}}/sales/check-duplicate", "host": ["{{baseUrl}}"], "path": ["sales", "check-duplicate"]}, "description": "Check if a sales record already exists for a given restaurant and date."}, "response": []}]}, {"name": "Analysis", "item": [{"name": "Get Headquarters Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/analysis/hq", "host": ["{{baseUrl}}"], "path": ["analysis", "hq"]}, "description": "Get headquarters analytics (Admin only)."}, "response": []}, {"name": "Get Branch Outlet Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/analysis/restaurant/{{restaurantId}}", "host": ["{{baseUrl}}"], "path": ["analysis", "restaurant", "{{restaurantId}}"]}, "description": "Get branch outlet analytics (Staff, Admin)."}, "response": []}, {"name": "Get KPI Progress for Branch", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/analysis/kpi/{{restaurantId}}", "host": ["{{baseUrl}}"], "path": ["analysis", "kpi", "{{restaurantId}}"]}, "description": "Get KPI progress for a branch (Staff, Admin)."}, "response": []}, {"name": "Get Dashboard Analytics (Admin)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/analysis/dashboard", "host": ["{{baseUrl}}"], "path": ["analysis", "dashboard"]}, "description": "Get comprehensive dashboard analytics (Admin only)."}, "response": []}]}, {"name": "Dashboard", "item": [{"name": "Get Admin Dashboard Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/dashboard/admin", "host": ["{{baseUrl}}"], "path": ["dashboard", "admin"]}, "description": "Get admin dashboard data (Admin only)."}, "response": []}, {"name": "Get User Dashboard Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/dashboard/user", "host": ["{{baseUrl}}"], "path": ["dashboard", "user"]}, "description": "Get user dashboard data (All authenticated users)."}, "response": []}, {"name": "Get User Sales Trend", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/dashboard/user/sales-trend", "host": ["{{baseUrl}}"], "path": ["dashboard", "user", "sales-trend"]}, "description": "Get user sales trend data for charts."}, "response": []}, {"name": "Get User Sales Breakdown", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/dashboard/user/sales-breakdown", "host": ["{{baseUrl}}"], "path": ["dashboard", "user", "sales-breakdown"]}, "description": "Get user sales breakdown data for charts."}, "response": []}, {"name": "Get Dashboard Overview (Analytics)", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/dashboard/overview", "host": ["{{baseUrl}}"], "path": ["dashboard", "overview"]}, "description": "Get dashboard overview data (analytics) (Staff and Admin)."}, "response": []}, {"name": "Get Recent Activity", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/dashboard/activity", "host": ["{{baseUrl}}"], "path": ["dashboard", "activity"]}, "description": "Get recent activity data (All authenticated users)."}, "response": []}, {"name": "Get Dashboard Metrics Summary", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/dashboard/metrics", "host": ["{{baseUrl}}"], "path": ["dashboard", "metrics"]}, "description": "Get dashboard metrics summary (Admin and Staff)."}, "response": []}, {"name": "Get Sales Trend Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/dashboard/sales-trend", "host": ["{{baseUrl}}"], "path": ["dashboard", "sales-trend"]}, "description": "Get sales trend data for charts (All authenticated users)."}, "response": []}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Get All Audit Logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/audit-logs?page=1&limit=10&action=create&resourceType=user", "host": ["{{baseUrl}}"], "path": ["audit-logs"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "action", "value": "create"}, {"key": "resourceType", "value": "user"}]}, "description": "Get all audit logs with pagination and filtering (Admin only)."}, "response": []}, {"name": "Get Audit Log Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/audit-logs/stats?startDate=2024-01-01&endDate=2024-12-31", "host": ["{{baseUrl}}"], "path": ["audit-logs", "stats"], "query": [{"key": "startDate", "value": "2024-01-01"}, {"key": "endDate", "value": "2024-12-31"}]}, "description": "Get audit log statistics (Admin only)."}, "response": []}, {"name": "Get User Activity Logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/audit-logs/user/{{userId}}?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["audit-logs", "user", "{{userId}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get audit logs for a specific user (Admin only)."}, "response": []}, {"name": "Get Resource Audit Logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/audit-logs/resource/user/{{userId}}?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["audit-logs", "resource", "user", "{{userId}}"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}, "description": "Get audit logs for a specific resource (Admin only)."}, "response": []}, {"name": "Get Audit Log by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/audit-logs/{{auditLogId}}", "host": ["{{baseUrl}}"], "path": ["audit-logs", "{{auditLogId}}"]}, "description": "Get audit log by ID (Admin only)."}, "response": []}, {"name": "Create Audi<PERSON> Log Entry", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"action\": \"create\",\n  \"resourceType\": \"user\",\n  \"resourceId\": \"{{userId}}\",\n  \"oldValues\": null,\n  \"newValues\": {\n    \"username\": \"newuser\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"ipAddress\": \"***********\",\n  \"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\"\n}"}, "url": {"raw": "{{baseUrl}}/audit-logs", "host": ["{{baseUrl}}"], "path": ["audit-logs"]}, "description": "Create a new audit log entry (Admin only - for manual entries)."}, "response": []}]}, {"name": "System", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}, "description": "Check server health status."}, "response": []}, {"name": "API Information", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/", "host": ["{{baseUrl}}"], "path": [""]}, "description": "Get API information and available endpoints."}, "response": []}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Get All User Roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/roles", "host": ["{{baseUrl}}"], "path": ["roles"]}, "description": "Get all user roles (All authenticated users)."}, "response": []}, {"name": "Get All Departments", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/departments", "host": ["{{baseUrl}}"], "path": ["departments"]}, "description": "Get all departments (All authenticated users)."}, "response": []}]}]}